package com.personal.blog.controller;

import com.personal.blog.service.FileService;
import com.personal.blog.vo.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 文件上传控制器
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Tag(name = "文件管理", description = "文件上传相关接口")
@RestController
@RequestMapping("/api/files")
@RequiredArgsConstructor
public class FileController {

    private final FileService fileService;

    @Operation(summary = "上传头像")
    @PostMapping("/avatar")
    public Result<Map<String, String>> uploadAvatar(
            @Parameter(description = "头像文件") @RequestParam("file") MultipartFile file) {
        try {
            if (file == null || file.isEmpty()) {
                return Result.error("请选择要上传的文件");
            }
            Map<String, String> result = fileService.uploadAvatar(file);
            return Result.success("头像上传成功", result);
        } catch (Exception e) {
            return Result.error("头像上传失败: " + e.getMessage());
        }
    }

    @Operation(summary = "上传文章图片")
    @PostMapping("/article-image")
    public Result<Map<String, String>> uploadArticleImage(
            @Parameter(description = "图片文件") @RequestParam("file") MultipartFile file) {
        try {
            if (file == null || file.isEmpty()) {
                return Result.error("请选择要上传的文件");
            }
            Map<String, String> result = fileService.uploadArticleImage(file);
            return Result.success("图片上传成功", result);
        } catch (Exception e) {
            return Result.error("图片上传失败: " + e.getMessage());
        }
    }

    @Operation(summary = "上传通用文件")
    @PostMapping("/upload")
    public Result<Map<String, String>> uploadFile(
            @Parameter(description = "文件") @RequestParam("file") MultipartFile file) {
        try {
            if (file == null || file.isEmpty()) {
                return Result.error("请选择要上传的文件");
            }
            Map<String, String> result = fileService.uploadFile(file);
            return Result.success("文件上传成功", result);
        } catch (Exception e) {
            return Result.error("文件上传失败: " + e.getMessage());
        }
    }

    @Operation(summary = "删除文件")
    @DeleteMapping("/delete")
    public Result<String> deleteFile(@Parameter(description = "文件URL") @RequestParam String fileUrl) {
        boolean success = fileService.deleteFile(fileUrl);
        if (success) {
            return Result.success("文件删除成功");
        } else {
            return Result.error("文件删除失败");
        }
    }
} 