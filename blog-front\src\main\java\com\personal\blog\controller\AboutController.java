package com.personal.blog.controller;

import com.personal.blog.dto.AboutUpdateDTO;
import com.personal.blog.service.AboutInfoService;
import com.personal.blog.vo.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import java.util.Map;

/**
 * 关于页面控制器
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Tag(name = "关于页面", description = "关于页面相关接口")
@RestController
@RequestMapping("/api/about")
@RequiredArgsConstructor
public class AboutController {

    private final AboutInfoService aboutInfoService;

    @Operation(summary = "获取关于页面信息")
    @GetMapping("/info")
    public Result<Map<String, Object>> getAboutInfo() {
        Map<String, Object> aboutInfo = aboutInfoService.getAboutInfo();
        return Result.success("获取成功", aboutInfo);
    }

    @Operation(summary = "更新关于页面信息")
    @PutMapping("/info")
    public Result<Map<String, Object>> updateAboutInfo(@Valid @RequestBody AboutUpdateDTO aboutUpdateDTO) {
        // 这里可以添加权限验证，只有管理员才能更新
        Map<String, Object> aboutInfo = aboutInfoService.updateAboutInfo(aboutUpdateDTO);
        return Result.success("关于页面信息更新成功", aboutInfo);
    }
} 