package com.personal.blog.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.personal.blog.entity.Article;
import com.personal.blog.entity.UserFavorite;
import com.personal.blog.mapper.ArticleMapper;
import com.personal.blog.mapper.UserFavoriteMapper;
import com.personal.blog.service.UserFavoriteService;
import com.personal.blog.vo.ArticleVO;
import com.personal.blog.vo.PageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户收藏Service实现类
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Service
@RequiredArgsConstructor
public class UserFavoriteServiceImpl extends ServiceImpl<UserFavoriteMapper, UserFavorite> implements UserFavoriteService {

    private final ArticleMapper articleMapper;

    @Override
    @Transactional
    public boolean favoriteArticle(Long articleId) {
        Long currentUserId = StpUtil.getLoginIdAsLong();
        
        // 检查文章是否存在
        Article article = articleMapper.selectById(articleId);
        if (article == null) {
            return false;
        }
        
        // 检查是否已收藏
        UserFavorite existFavorite = this.getOne(new LambdaQueryWrapper<UserFavorite>()
                .eq(UserFavorite::getUserId, currentUserId)
                .eq(UserFavorite::getArticleId, articleId));
        
        if (existFavorite != null) {
            if (existFavorite.getStatus() == 1) {
                return true; // 已经收藏了
            } else {
                // 更新为已收藏
                existFavorite.setStatus(1);
                this.updateById(existFavorite);
            }
        } else {
            // 创建新的收藏记录
            UserFavorite userFavorite = new UserFavorite();
            userFavorite.setUserId(currentUserId);
            userFavorite.setArticleId(articleId);
            userFavorite.setStatus(1);
            this.save(userFavorite);
        }
        
        return true;
    }

    @Override
    @Transactional
    public boolean unfavoriteArticle(Long articleId) {
        Long currentUserId = StpUtil.getLoginIdAsLong();
        
        // 检查是否已收藏
        UserFavorite existFavorite = this.getOne(new LambdaQueryWrapper<UserFavorite>()
                .eq(UserFavorite::getUserId, currentUserId)
                .eq(UserFavorite::getArticleId, articleId));
        
        if (existFavorite == null || existFavorite.getStatus() == 0) {
            return true; // 本来就没有收藏
        }
        
        // 更新为取消收藏
        existFavorite.setStatus(0);
        this.updateById(existFavorite);
        
        return true;
    }

    @Override
    public boolean isFavorited(Long articleId) {
        Long currentUserId = StpUtil.getLoginIdAsLong();
        
        UserFavorite userFavorite = this.getOne(new LambdaQueryWrapper<UserFavorite>()
                .eq(UserFavorite::getUserId, currentUserId)
                .eq(UserFavorite::getArticleId, articleId));
        
        return userFavorite != null && userFavorite.getStatus() == 1;
    }

    @Override
    public PageResult<ArticleVO> getUserFavoriteArticles(Integer current, Integer size) {
        Long currentUserId = StpUtil.getLoginIdAsLong();
        
        Page<ArticleVO> page = new Page<>(current, size);
        IPage<ArticleVO> result = baseMapper.selectUserFavoriteArticles(page, currentUserId);
        
        return new PageResult<>(result.getRecords(), result.getTotal(), 
                result.getCurrent(), result.getSize());
    }

    @Override
    public void removeFavorite(Long articleId) {
        Long currentUserId = StpUtil.getLoginIdAsLong();
        
        // 检查是否已收藏
        UserFavorite existFavorite = this.getOne(new LambdaQueryWrapper<UserFavorite>()
                .eq(UserFavorite::getUserId, currentUserId)
                .eq(UserFavorite::getArticleId, articleId));
        
        if (existFavorite == null || existFavorite.getStatus() == 0) {
            return; // 本来就没有收藏
        }
        
        // 更新为取消收藏
        existFavorite.setStatus(0);
        this.updateById(existFavorite);
    }
} 