<template>
  <div class="user-center-page">
    <div class="user-header">
      <div class="user-info">
        <img :src="getAvatarUrl(userStore.avatar)" :alt="userStore.nickname" class="user-avatar">
        <div class="user-details">
          <h1 class="user-name">{{ userStore.nickname }}</h1>
          <p class="user-email">{{ userStore.userInfo.email }}</p>
        </div>
      </div>
      <div class="user-actions">
        <el-button type="primary" @click="showEditDialog = true">
          编辑资料
        </el-button>
      </div>
    </div>

    <div class="user-content">
      <el-tabs v-model="activeTab" class="user-tabs" @tab-change="handleTabChange">
        <el-tab-pane label="个人信息" name="profile">
          <div class="profile-section">
            <div class="info-item">
              <label>用户名：</label>
              <span>{{ userStore.userInfo.username }}</span>
            </div>
            <div class="info-item">
              <label>昵称：</label>
              <span>{{ userStore.userInfo.nickname || '未设置' }}</span>
            </div>
            <div class="info-item">
              <label>邮箱：</label>
              <span>{{ userStore.userInfo.email }}</span>
            </div>
            <div class="info-item">
              <label>注册时间：</label>
              <span>{{ formatDate(userStore.userInfo.createTime) }}</span>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="我的文章" name="articles">
          <div class="articles-section">
            <div class="section-header">
              <h3>我的文章 ({{ articlesPagination.total }})</h3>
            </div>
            <div class="articles-list" v-loading="articlesLoading">
              <div v-if="articles.length === 0 && !articlesLoading" class="empty-state">
                <el-empty description="暂无文章" />
              </div>
              <div v-else class="article-items">
                <div v-for="article in articles" :key="article.id" class="article-item">
                  <div class="article-info">
                    <h4 class="article-title" @click="$router.push(`/article/${article.id}`)">
                      {{ article.title }}
                    </h4>
                    <p class="article-summary">{{ article.summary }}</p>
                    <div class="article-meta">
                      <span class="meta-item">
                        <el-icon><View /></el-icon>
                        {{ article.viewCount || 0 }} 阅读
                      </span>
                      <span class="meta-item">
                        <el-icon><ChatDotRound /></el-icon>
                        {{ article.commentCount || 0 }} 评论
                      </span>
                      <span class="meta-item">
                        <el-icon><Star /></el-icon>
                        {{ article.likeCount || 0 }} 点赞
                      </span>
                      <span class="meta-item">
                        <el-icon><Calendar /></el-icon>
                        {{ formatDate(article.createTime) }}
                      </span>
                    </div>
                  </div>
                  <div class="article-actions">
                    <el-button size="small" @click="$router.push(`/article/${article.id}`)">
                      查看
                    </el-button>
                  </div>
                </div>
              </div>
              <div v-if="articles.length > 0" class="pagination-wrapper">
                <el-pagination
                  v-model:current-page="articlesPagination.current"
                  v-model:page-size="articlesPagination.size"
                  :total="articlesPagination.total"
                  :page-sizes="[10, 20, 50]"
                  layout="total, sizes, prev, pager, next"
                  @size-change="handleArticlesSizeChange"
                  @current-change="handleArticlesCurrentChange"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="我的评论" name="comments">
          <div class="comments-section">
            <div class="section-header">
              <h3>我的评论 ({{ commentsPagination.total }})</h3>
            </div>
            <div class="comments-list" v-loading="commentsLoading">
              <div v-if="comments.length === 0 && !commentsLoading" class="empty-state">
                <el-empty description="暂无评论" />
              </div>
              <div v-else class="comment-items">
                <div v-for="comment in comments" :key="comment.id" class="comment-item">
                  <div class="comment-info">
                    <div class="comment-content">
                      <span class="comment-text">{{ comment.content }}</span>
                      <span class="comment-time">{{ formatDate(comment.createTime) }}</span>
                    </div>
                    <div class="comment-article">
                      评论文章：<span class="article-link" @click="$router.push(`/article/${comment.articleId}`)">
                        {{ comment.articleTitle }}
                      </span>
                    </div>
                  </div>
                  <div class="comment-actions">
                    <el-button size="small" type="danger" @click="deleteComment(comment.id)">
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
              <div v-if="comments.length > 0" class="pagination-wrapper">
                <el-pagination
                  v-model:current-page="commentsPagination.current"
                  v-model:page-size="commentsPagination.size"
                  :total="commentsPagination.total"
                  :page-sizes="[10, 20, 50]"
                  layout="total, sizes, prev, pager, next"
                  @size-change="handleCommentsSizeChange"
                  @current-change="handleCommentsCurrentChange"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="我的收藏" name="favorites">
          <div class="favorites-section">
            <div class="section-header">
              <h3>我的收藏 ({{ favoritesPagination.total }})</h3>
            </div>
            <div class="favorites-list" v-loading="favoritesLoading">
              <div v-if="favorites.length === 0 && !favoritesLoading" class="empty-state">
                <el-empty description="暂无收藏" />
              </div>
              <div v-else class="favorite-items">
                <div v-for="article in favorites" :key="article.id" class="favorite-item">
                  <div class="article-info">
                    <h4 class="article-title" @click="$router.push(`/article/${article.id}`)">
                      {{ article.title }}
                    </h4>
                    <p class="article-summary">{{ article.summary }}</p>
                    <div class="article-meta">
                      <span class="meta-item">
                        <el-icon><View /></el-icon>
                        {{ article.viewCount || 0 }} 阅读
                      </span>
                      <span class="meta-item">
                        <el-icon><ChatDotRound /></el-icon>
                        {{ article.commentCount || 0 }} 评论
                      </span>
                      <span class="meta-item">
                        <el-icon><Star /></el-icon>
                        {{ article.likeCount || 0 }} 点赞
                      </span>
                      <span class="meta-item">
                        <el-icon><Calendar /></el-icon>
                        {{ formatDate(article.createTime) }}
                      </span>
                    </div>
                  </div>
                  <div class="article-actions">
                    <el-button size="small" @click="$router.push(`/article/${article.id}`)">
                      查看
                    </el-button>
                    <el-button size="small" type="danger" @click="removeFavorite(article.id)">
                      取消收藏
                    </el-button>
                  </div>
                </div>
              </div>
              <div v-if="favorites.length > 0" class="pagination-wrapper">
                <el-pagination
                  v-model:current-page="favoritesPagination.current"
                  v-model:page-size="favoritesPagination.size"
                  :total="favoritesPagination.total"
                  :page-sizes="[10, 20, 50]"
                  layout="total, sizes, prev, pager, next"
                  @size-change="handleFavoritesSizeChange"
                  @current-change="handleFavoritesCurrentChange"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="我的点赞" name="likes">
          <div class="likes-section">
            <div class="section-header">
              <h3>我的点赞 ({{ likesPagination.total }})</h3>
            </div>
            <div class="likes-list" v-loading="likesLoading">
              <div v-if="likes.length === 0 && !likesLoading" class="empty-state">
                <el-empty description="暂无点赞" />
              </div>
              <div v-else class="like-items">
                <div v-for="article in likes" :key="article.id" class="like-item">
                  <div class="article-info">
                    <h4 class="article-title" @click="$router.push(`/article/${article.id}`)">
                      {{ article.title }}
                    </h4>
                    <p class="article-summary">{{ article.summary }}</p>
                    <div class="article-meta">
                      <span class="meta-item">
                        <el-icon><View /></el-icon>
                        {{ article.viewCount || 0 }} 阅读
                      </span>
                      <span class="meta-item">
                        <el-icon><ChatDotRound /></el-icon>
                        {{ article.commentCount || 0 }} 评论
                      </span>
                      <span class="meta-item">
                        <el-icon><Star /></el-icon>
                        {{ article.likeCount || 0 }} 点赞
                      </span>
                      <span class="meta-item">
                        <el-icon><Calendar /></el-icon>
                        {{ formatDate(article.createTime) }}
                      </span>
                    </div>
                  </div>
                  <div class="article-actions">
                    <el-button size="small" @click="$router.push(`/article/${article.id}`)">
                      查看
                    </el-button>
                    <el-button size="small" type="danger" @click="removeLike(article.id)">
                      取消点赞
                    </el-button>
                  </div>
                </div>
              </div>
              <div v-if="likes.length > 0" class="pagination-wrapper">
                <el-pagination
                  v-model:current-page="likesPagination.current"
                  v-model:page-size="likesPagination.size"
                  :total="likesPagination.total"
                  :page-sizes="[10, 20, 50]"
                  layout="total, sizes, prev, pager, next"
                  @size-change="handleLikesSizeChange"
                  @current-change="handleLikesCurrentChange"
                />
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 编辑资料对话框 -->
    <el-dialog
      v-model="showEditDialog"
      title="编辑资料"
      width="500px"
      :before-close="handleCloseEdit"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="80px"
      >
        <el-form-item label="头像">
          <div class="avatar-upload">
            <img :src="getAvatarUrl(editForm.avatar)" class="avatar-preview">
            <FileUpload
              ref="avatarUploadRef"
              accept=".jpg,.jpeg,.png,.gif,.webp"
              :max-size="5"
              placeholder="点击或拖拽头像到此处"
              description="支持 jpg、png、gif、webp 格式，文件大小不超过 5MB"
              :auto-upload="true"
              :upload-function="handleAvatarUpload"
              @upload-success="handleAvatarUploadSuccess"
              @upload-error="handleAvatarUploadError"
            />
            <div class="avatar-test">
              <el-button size="small" @click="testAvatarUrl">测试头像URL</el-button>
              <span v-if="currentAvatarUrl" class="avatar-url">{{ currentAvatarUrl }}</span>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="editForm.nickname" placeholder="请输入昵称" />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" placeholder="请输入邮箱" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showEditDialog = false">取消</el-button>
        <el-button type="primary" @click="handleSaveEdit" :loading="saving">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { 
  getUserProfile, 
  updateUserProfile, 
  getUserArticles, 
  getUserComments, 
  getUserFavoriteArticles, 
  getUserLikedArticles,
  deleteUserComment,
  removeFavorite as removeFavoriteApi,
  removeLike as removeLikeApi
} from '@/api/user'
import { uploadAvatar } from '@/api/file'
import { ElMessage, ElMessageBox } from 'element-plus'
import { View, ChatDotRound, Star, Calendar } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import FileUpload from '@/components/common/FileUpload.vue'
import { getAvatarUrl } from '@/utils/avatar'

const userStore = useUserStore()

const activeTab = ref('profile')
const showEditDialog = ref(false)
const saving = ref(false)
const editFormRef = ref()
const avatarUploadRef = ref()
const currentAvatarUrl = ref('')

// 编辑表单
const editForm = reactive({
  avatar: '',
  nickname: '',
  email: ''
})

// 表单验证规则
const editRules = {
  nickname: [
    { max: 50, message: '昵称长度不能超过50个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
}

// 文章相关
const articles = ref([])
const articlesLoading = ref(false)
const articlesPagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 评论相关
const comments = ref([])
const commentsLoading = ref(false)
const commentsPagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 收藏相关
const favorites = ref([])
const favoritesLoading = ref(false)
const favoritesPagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 点赞相关
const likes = ref([])
const likesLoading = ref(false)
const likesPagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 格式化日期
const formatDate = (date) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

// 头像上传处理函数
const handleAvatarUpload = async (file) => {
  try {
    const response = await uploadAvatar(file)
    return response.data
  } catch (error) {
    throw new Error('头像上传失败')
  }
}

// 头像上传成功
const handleAvatarUploadSuccess = (result) => {
  // 保存相对路径
  editForm.avatar = result.url
  ElMessage.success('头像上传成功')
}

// 头像上传失败
const handleAvatarUploadError = (error) => {
  ElMessage.error('头像上传失败')
}

// 测试头像URL
const testAvatarUrl = () => {
  const url = getAvatarUrl(editForm.avatar)
  currentAvatarUrl.value = url
  ElMessage.info(`当前头像URL: ${url}`)
}





// 处理关闭编辑对话框
const handleCloseEdit = () => {
  showEditDialog.value = false
  // 重置表单
  Object.assign(editForm, {
    avatar: userStore.avatar,
    nickname: userStore.userInfo.nickname || '',
    email: userStore.userInfo.email || ''
  })
}

// 保存编辑
const handleSaveEdit = async () => {
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()
    
    saving.value = true
    
    await updateUserProfile({
      nickname: editForm.nickname,
      email: editForm.email,
      avatar: editForm.avatar
    })
    
    // 更新本地用户信息
    userStore.setUserInfo({
      ...userStore.userInfo,
      nickname: editForm.nickname,
      email: editForm.email,
      avatar: editForm.avatar
    })
    
    // 更新用户store中的头像
    userStore.setAvatar(editForm.avatar)
    
    ElMessage.success('资料更新成功')
    showEditDialog.value = false
  } catch (error) {
    ElMessage.error('资料更新失败')
  } finally {
    saving.value = false
  }
}

// 获取用户文章
const fetchUserArticles = async () => {
  try {
    articlesLoading.value = true
    const response = await getUserArticles({
      current: articlesPagination.current,
      size: articlesPagination.size
    })
    articles.value = response.data.records
    articlesPagination.total = response.data.total
  } catch (error) {
    console.error('获取用户文章失败:', error)
    ElMessage.error('获取用户文章失败')
  } finally {
    articlesLoading.value = false
  }
}

// 获取用户评论
const fetchUserComments = async () => {
  try {
    commentsLoading.value = true
    const response = await getUserComments({
      current: commentsPagination.current,
      size: commentsPagination.size
    })
    comments.value = response.data.records
    commentsPagination.total = response.data.total
  } catch (error) {
    console.error('获取用户评论失败:', error)
    ElMessage.error('获取用户评论失败')
  } finally {
    commentsLoading.value = false
  }
}

// 获取用户收藏
const fetchUserFavorites = async () => {
  try {
    favoritesLoading.value = true
    const response = await getUserFavoriteArticles({
      current: favoritesPagination.current,
      size: favoritesPagination.size
    })
    favorites.value = response.data.records
    favoritesPagination.total = response.data.total
  } catch (error) {
    console.error('获取用户收藏失败:', error)
    ElMessage.error('获取用户收藏失败')
  } finally {
    favoritesLoading.value = false
  }
}

// 获取用户点赞
const fetchUserLikes = async () => {
  try {
    likesLoading.value = true
    const response = await getUserLikedArticles({
      current: likesPagination.current,
      size: likesPagination.size
    })
    likes.value = response.data.records
    likesPagination.total = response.data.total
  } catch (error) {
    console.error('获取用户点赞失败:', error)
    ElMessage.error('获取用户点赞失败')
  } finally {
    likesLoading.value = false
  }
}

// 删除评论
const deleteComment = async (commentId) => {
  try {
    await ElMessageBox.confirm('确定要删除这条评论吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await deleteUserComment(commentId)
    ElMessage.success('评论删除成功')
    await fetchUserComments()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除评论失败')
    }
  }
}

// 取消收藏
const removeFavorite = async (articleId) => {
  try {
    await ElMessageBox.confirm('确定要取消收藏这篇文章吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await removeFavoriteApi(articleId)
    ElMessage.success('取消收藏成功')
    await fetchUserFavorites()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消收藏失败')
    }
  }
}

// 取消点赞
const removeLike = async (articleId) => {
  try {
    await ElMessageBox.confirm('确定要取消点赞这篇文章吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await removeLikeApi(articleId)
    ElMessage.success('取消点赞成功')
    await fetchUserLikes()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消点赞失败')
    }
  }
}

// 处理标签页切换
const handleTabChange = (tabName) => {
  switch (tabName) {
    case 'articles':
      if (articles.value.length === 0) {
        fetchUserArticles()
      }
      break
    case 'comments':
      if (comments.value.length === 0) {
        fetchUserComments()
      }
      break
    case 'favorites':
      if (favorites.value.length === 0) {
        fetchUserFavorites()
      }
      break
    case 'likes':
      if (likes.value.length === 0) {
        fetchUserLikes()
      }
      break
  }
}

// 分页处理函数
const handleArticlesSizeChange = (size) => {
  articlesPagination.size = size
  articlesPagination.current = 1
  fetchUserArticles()
}

const handleArticlesCurrentChange = (current) => {
  articlesPagination.current = current
  fetchUserArticles()
}

const handleCommentsSizeChange = (size) => {
  commentsPagination.size = size
  commentsPagination.current = 1
  fetchUserComments()
}

const handleCommentsCurrentChange = (current) => {
  commentsPagination.current = current
  fetchUserComments()
}

const handleFavoritesSizeChange = (size) => {
  favoritesPagination.size = size
  favoritesPagination.current = 1
  fetchUserFavorites()
}

const handleFavoritesCurrentChange = (current) => {
  favoritesPagination.current = current
  fetchUserFavorites()
}

const handleLikesSizeChange = (size) => {
  likesPagination.size = size
  likesPagination.current = 1
  fetchUserLikes()
}

const handleLikesCurrentChange = (current) => {
  likesPagination.current = current
  fetchUserLikes()
}

onMounted(() => {
  // 初始化编辑表单
  Object.assign(editForm, {
    avatar: userStore.avatar,
    nickname: userStore.userInfo.nickname || '',
    email: userStore.userInfo.email || ''
  })
})
</script>

<style scoped>
.user-center-page {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
}

.user-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
  padding: var(--spacing-xl);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
  color: white;
  position: relative;
  overflow: hidden;
}

.user-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  position: relative;
  z-index: 1;
}

.user-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid rgba(255, 255, 255, 0.3);
  box-shadow: var(--shadow-lg);
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
  border-color: rgba(255, 255, 255, 0.5);
}

.user-name {
  font-size: var(--font-3xl);
  font-weight: 700;
  color: white;
  margin-bottom: var(--spacing-xs);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-email {
  color: rgba(255, 255, 255, 0.9);
  font-size: var(--font-md);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-actions {
  position: relative;
  z-index: 1;
}

.user-content {
  background: var(--bg-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  border: 1px solid var(--border-light);
}

.user-tabs {
  padding: 0;
}

.user-tabs :deep(.el-tabs__header) {
  margin: 0;
  background: var(--bg-gray);
  border-bottom: 1px solid var(--border-light);
}

.user-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0 var(--spacing-lg);
}

.user-tabs :deep(.el-tabs__item) {
  font-weight: 600;
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.user-tabs :deep(.el-tabs__item.is-active) {
  color: var(--primary-color);
  font-weight: 700;
}

.user-tabs :deep(.el-tabs__active-bar) {
  background-color: var(--primary-color);
  height: 3px;
  border-radius: 2px;
}

.user-tabs :deep(.el-tabs__content) {
  padding: var(--spacing-xl);
}

.profile-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.info-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--bg-gray) 0%, var(--bg-white) 100%);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
}

.info-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--primary-color);
}

.info-item label {
  font-weight: 700;
  color: var(--primary-color);
  min-width: 120px;
  font-size: var(--font-md);
}

.info-item span {
  color: var(--text-primary);
  font-weight: 500;
  flex: 1;
}

.section-header {
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 2px solid var(--border-light);
  position: relative;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  border-radius: 1px;
}

.section-header h3 {
  margin: 0;
  color: var(--primary-color);
  font-size: var(--font-xl);
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.section-header h3::before {
  content: '';
  width: 4px;
  height: 20px;
  background: linear-gradient(180deg, var(--primary-color), var(--accent-color));
  border-radius: 2px;
}

.articles-section,
.comments-section,
.favorites-section,
.likes-section {
  min-height: 300px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  background: linear-gradient(135deg, var(--bg-gray) 0%, var(--bg-white) 100%);
  border-radius: var(--radius-lg);
  border: 2px dashed var(--border-light);
}

.article-items,
.comment-items,
.favorite-items,
.like-items {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.article-item,
.favorite-item,
.like-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--bg-white) 0%, var(--bg-gray) 100%);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.article-item::before,
.favorite-item::before,
.like-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, var(--primary-color), var(--accent-color));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.article-item:hover,
.favorite-item:hover,
.like-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.article-item:hover::before,
.favorite-item:hover::before,
.like-item:hover::before {
  opacity: 1;
}

.article-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding-left: var(--spacing-md);
}

.article-title {
  margin: 0;
  font-size: var(--font-lg);
  font-weight: 700;
  color: var(--primary-color);
  cursor: pointer;
  transition: all 0.3s ease;
  line-height: 1.4;
}

.article-title:hover {
  color: var(--accent-color);
  transform: translateX(4px);
}

.article-summary {
  margin: 0;
  color: var(--text-secondary);
  font-size: var(--font-sm);
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  opacity: 0.8;
}

.article-meta {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
  margin-top: var(--spacing-sm);
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--text-secondary);
  font-size: var(--font-xs);
  font-weight: 500;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(0, 0, 0, 0.05);
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
}

.meta-item:hover {
  background: rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.article-actions {
  display: flex;
  gap: var(--spacing-sm);
  flex-shrink: 0;
  align-items: flex-start;
}

.comment-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--bg-white) 0%, var(--bg-gray) 100%);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.comment-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, var(--accent-color), var(--primary-color));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.comment-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--accent-color);
}

.comment-item:hover::before {
  opacity: 1;
}

.comment-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding-left: var(--spacing-md);
}

.comment-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-lg);
}

.comment-text {
  flex: 1;
  color: var(--text-primary);
  font-size: var(--font-md);
  line-height: 1.6;
  font-weight: 500;
  background: rgba(0, 0, 0, 0.03);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  border-left: 3px solid var(--accent-color);
}

.comment-time {
  color: var(--text-secondary);
  font-size: var(--font-xs);
  flex-shrink: 0;
  font-weight: 500;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: rgba(0, 0, 0, 0.05);
  border-radius: var(--radius-sm);
}

.comment-article {
  color: var(--text-secondary);
  font-size: var(--font-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.comment-article::before {
  content: '📄';
  font-size: var(--font-sm);
}

.article-link {
  color: var(--primary-color);
  cursor: pointer;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  background: rgba(0, 0, 0, 0.05);
}

.article-link:hover {
  color: var(--accent-color);
  background: rgba(0, 0, 0, 0.1);
  transform: translateX(2px);
}

.comment-actions {
  display: flex;
  gap: var(--spacing-sm);
  flex-shrink: 0;
  align-items: flex-start;
}

.pagination-wrapper {
  margin-top: var(--spacing-xl);
  display: flex;
  justify-content: center;
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--bg-gray) 0%, var(--bg-white) 100%);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.pagination-wrapper :deep(.el-pagination) {
  --el-pagination-font-size: var(--font-sm);
  --el-pagination-button-color: var(--text-secondary);
  --el-pagination-button-bg-color: transparent;
  --el-pagination-button-disabled-color: var(--text-secondary);
  --el-pagination-button-disabled-bg-color: transparent;
  --el-pagination-hover-color: var(--primary-color);
  --el-pagination-hover-bg-color: rgba(0, 0, 0, 0.05);
}

.pagination-wrapper :deep(.el-pagination .el-pager li) {
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
}

.pagination-wrapper :deep(.el-pagination .el-pager li.is-active) {
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  color: white;
  font-weight: 700;
}

.avatar-upload {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--bg-gray) 0%, var(--bg-white) 100%);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
}

.avatar-preview {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--primary-color);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  align-self: center;
}

.avatar-preview:hover {
  transform: scale(1.05);
  border-color: var(--accent-color);
}

.avatar-test {
  margin-top: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.avatar-url {
  font-size: var(--font-xs);
  color: var(--text-secondary);
  word-break: break-all;
  max-width: 200px;
}

/* 移动端样式 */
@media (max-width: 768px) {
  .user-center-page {
    padding: var(--spacing-md);
  }

  .user-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: var(--spacing-lg);
    gap: var(--spacing-md);
  }

  .user-info {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }

  .user-avatar {
    width: 80px;
    height: 80px;
  }

  .user-name {
    font-size: var(--font-xl);
  }

  .user-tabs :deep(.el-tabs__content) {
    padding: var(--spacing-md);
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
    padding: var(--spacing-md);
  }

  .info-item label {
    min-width: auto;
    font-size: var(--font-sm);
  }

  .article-item,
  .favorite-item,
  .like-item,
  .comment-item {
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .article-info,
  .comment-info {
    padding-left: 0;
  }

  .article-actions,
  .comment-actions {
    justify-content: flex-end;
    width: 100%;
  }

  .comment-content {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .article-meta {
    gap: var(--spacing-sm);
    justify-content: flex-start;
  }

  .meta-item {
    font-size: var(--font-xs);
    padding: var(--spacing-xs);
  }

  .section-header h3 {
    font-size: var(--font-lg);
  }

  .pagination-wrapper {
    padding: var(--spacing-md);
  }

  .avatar-upload {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
    padding: var(--spacing-md);
  }

  .avatar-preview {
    width: 80px;
    height: 80px;
  }
}
</style>
