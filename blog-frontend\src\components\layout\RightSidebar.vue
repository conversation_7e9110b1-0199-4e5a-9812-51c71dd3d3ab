<template>
  <aside class="right-sidebar">
    <!-- 热门文章推荐 -->
    <div class="hot-articles-card">
      <div class="card-header">
        <h4 class="card-title">
          <el-icon><Fire /></el-icon>
          热门文章
        </h4>
      </div>
      <div class="hot-articles-list" v-loading="loading">
        <div 
          v-for="(article, index) in hotArticles" 
          :key="article.id"
          class="hot-article-item"
          @click="$router.push(`/article/${article.id}`)"
        >
          <div class="article-rank">{{ index + 1 }}</div>
          <div class="article-info">
            <h5 class="article-title">{{ article.title }}</h5>
            <div class="article-meta">
              <span class="view-count">
                <el-icon><View /></el-icon>
                {{ article.viewCount || 0 }}
              </span>
              <span class="like-count">
                <el-icon><Star /></el-icon>
                {{ article.likeCount || 0 }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 文章分类导航 -->
    <div class="categories-card">
      <div class="card-header">
        <h4 class="card-title">
          <el-icon><Menu /></el-icon>
          文章分类
        </h4>
      </div>
      <div class="categories-list">
        <div 
          v-for="category in categories" 
          :key="category.id"
          class="category-item"
          @click="$router.push(`/category/${category.id}`)"
        >
          <div class="category-info">
            <span class="category-name">{{ category.name }}</span>
            <span class="category-count">({{ category.articleCount || 0 }})</span>
          </div>
          <div class="category-icon">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 标签云 -->
    <div class="tags-card">
      <div class="card-header">
        <h4 class="card-title">
          <el-icon><Discount /></el-icon>
          标签云
        </h4>
      </div>
      <div class="tags-cloud">
        <el-tag 
          v-for="tag in tags" 
          :key="tag.id"
          :type="getTagType(tag.id)"
          :effect="getTagEffect(tag.articleCount)"
          class="tag-item"
          :class="getTagSize(tag.articleCount)"
          @click="handleTagClick(tag)"
        >
          {{ tag.name }}
          <span class="tag-count">({{ tag.articleCount }})</span>
        </el-tag>
      </div>
    </div>

    <!-- 友情链接 -->
    <div class="links-card">
      <div class="card-header">
        <h4 class="card-title">
          <el-icon><Link /></el-icon>
          友情链接
        </h4>
      </div>
      <div class="links-list">
        <a 
          v-for="link in friendLinks" 
          :key="link.id"
          :href="link.url"
          target="_blank"
          class="link-item"
        >
          <div class="link-avatar">
            <img :src="link.avatar" :alt="link.name" />
          </div>
          <div class="link-info">
            <span class="link-name">{{ link.name }}</span>
            <span class="link-desc">{{ link.description }}</span>
          </div>
          <div class="link-icon">
            <el-icon><Top /></el-icon>
          </div>
        </a>
      </div>
    </div>

    <!-- 实用工具 -->
    <div class="tools-card">
      <div class="card-header">
        <h4 class="card-title">
          <el-icon><Tools /></el-icon>
          实用工具
        </h4>
      </div>
      <div class="tools-list">
        <div 
          v-for="tool in tools" 
          :key="tool.id"
          class="tool-item"
          @click="handleToolClick(tool)"
        >
          <div class="tool-icon">
            <el-icon :class="tool.iconClass">
              <component :is="tool.icon" />
            </el-icon>
          </div>
          <div class="tool-info">
            <span class="tool-name">{{ tool.name }}</span>
            <span class="tool-desc">{{ tool.description }}</span>
          </div>
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useArticleStore } from '@/store/article'
import { getHotArticles, getCategories } from '@/api/article'
import { ElMessage } from 'element-plus'

const router = useRouter()
const articleStore = useArticleStore()

const loading = ref(false)
const hotArticles = ref([])
const categories = ref([])
const tags = ref([])
const friendLinks = ref([])
const tools = ref([])

// 获取热门文章
const fetchHotArticles = async () => {
  try {
    loading.value = true
    // const response = await getHotArticles({ size: 5 })
    // hotArticles.value = response.data.records || []
  } catch (error) {
    console.error('获取热门文章失败:', error)
    // 使用模拟数据
    hotArticles.value = [
      { id: 1, title: 'Vue 3 Composition API 深度解析', viewCount: 1580, likeCount: 128 },
      { id: 2, title: 'Spring Boot 微服务架构实践', viewCount: 1420, likeCount: 96 },
      { id: 3, title: 'JavaScript 异步编程最佳实践', viewCount: 1350, likeCount: 85 },
      { id: 4, title: 'MySQL 性能优化技巧总结', viewCount: 1280, likeCount: 72 },
      { id: 5, title: 'Docker 容器化部署指南', viewCount: 1150, likeCount: 64 }
    ]
  } finally {
    loading.value = false
  }
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    const response = await getCategories()
    categories.value = response.data || []
  } catch (error) {
    console.error('获取分类失败:', error)
    // 使用模拟数据
    categories.value = [
      { id: 1, name: '前端开发', articleCount: 15 },
      { id: 2, name: '后端开发', articleCount: 12 },
      { id: 3, name: '数据库', articleCount: 8 },
      { id: 4, name: '运维部署', articleCount: 6 },
      { id: 5, name: '算法与数据结构', articleCount: 4 }
    ]
  }
}

// 初始化标签数据
const initTags = () => {
  tags.value = [
    { id: 1, name: 'Vue.js', articleCount: 12 },
    { id: 2, name: 'React', articleCount: 8 },
    { id: 3, name: 'JavaScript', articleCount: 15 },
    { id: 4, name: 'TypeScript', articleCount: 6 },
    { id: 5, name: 'Node.js', articleCount: 9 },
    { id: 6, name: 'Spring Boot', articleCount: 10 },
    { id: 7, name: 'MySQL', articleCount: 7 },
    { id: 8, name: 'Redis', articleCount: 5 },
    { id: 9, name: 'Docker', articleCount: 4 },
    { id: 10, name: 'Git', articleCount: 3 }
  ]
}

// 初始化友情链接
const initFriendLinks = () => {
  friendLinks.value = [
    {
      id: 1,
      name: 'Vue.js 官网',
      description: '渐进式 JavaScript 框架',
      url: 'https://vuejs.org',
      avatar: 'https://vuejs.org/logo.svg'
    },
    {
      id: 2,
      name: 'Element Plus',
      description: 'Vue 3 组件库',
      url: 'https://element-plus.org',
      avatar: 'https://element-plus.org/images/element-plus-logo-small.svg'
    },
    {
      id: 3,
      name: 'GitHub',
      description: '代码托管平台',
      url: 'https://github.com',
      avatar: 'https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png'
    }
  ]
}

// 初始化工具列表
const initTools = () => {
  tools.value = [
    {
      id: 1,
      name: '文章搜索',
      description: '快速查找文章',
      icon: 'Search',
      iconClass: 'text-blue',
      action: 'search'
    },
    {
      id: 2,
      name: '随机文章',
      description: '发现新内容',
      icon: 'Refresh',
      iconClass: 'text-green',
      action: 'random'
    },
    {
      id: 3,
      name: '文章归档',
      description: '按时间浏览',
      icon: 'Calendar',
      iconClass: 'text-orange',
      action: 'archive'
    },
    {
      id: 4,
      name: 'RSS 订阅',
      description: '订阅更新',
      icon: 'Promotion',
      iconClass: 'text-purple',
      action: 'rss'
    }
  ]
}

// 获取标签类型
const getTagType = (tagId) => {
  const types = ['', 'success', 'info', 'warning', 'danger']
  return types[tagId % types.length]
}

// 获取标签效果
const getTagEffect = (count) => {
  return count > 10 ? 'dark' : count > 5 ? 'light' : 'plain'
}

// 获取标签大小
const getTagSize = (count) => {
  if (count > 10) return 'large'
  if (count > 5) return 'medium'
  return 'small'
}

// 处理标签点击
const handleTagClick = (tag) => {
  router.push(`/search?tag=${tag.name}`)
}

// 处理工具点击
const handleToolClick = (tool) => {
  switch (tool.action) {
    case 'search':
      router.push('/search')
      break
    case 'random':
      // 随机跳转到一篇文章
      if (hotArticles.value.length > 0) {
        const randomIndex = Math.floor(Math.random() * hotArticles.value.length)
        router.push(`/article/${hotArticles.value[randomIndex].id}`)
      }
      break
    case 'archive':
      ElMessage.info('归档功能开发中...')
      break
    case 'rss':
      ElMessage.info('RSS 功能开发中...')
      break
  }
}

// 初始化数据
onMounted(async () => {
  await Promise.all([
    fetchHotArticles(),
    fetchCategories()
  ])
  
  initTags()
  initFriendLinks()
  initTools()
})
</script>

<style scoped>
.right-sidebar {
  width: 280px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

/* 卡片通用样式 */
.hot-articles-card,
.categories-card,
.tags-card,
.links-card,
.tools-card {
  background: var(--bg-white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
}

.hot-articles-card:hover,
.categories-card:hover,
.tags-card:hover,
.links-card:hover,
.tools-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  margin-bottom: var(--spacing-md);
}

.card-title {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0;
}

/* 热门文章 */
.hot-articles-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.hot-article-item {
  display: flex;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
}

.hot-article-item:hover {
  background: var(--bg-gray);
  transform: translateX(4px);
}

.article-rank {
  flex-shrink: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-xs);
  font-weight: 600;
}

.article-info {
  flex: 1;
  min-width: 0;
}

.article-title {
  font-size: var(--font-sm);
  font-weight: 500;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-meta {
  display: flex;
  gap: var(--spacing-md);
  font-size: var(--font-xs);
  color: var(--text-light);
}

.view-count,
.like-count {
  display: flex;
  align-items: center;
  gap: 2px;
}

/* 分类导航 */
.categories-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-item:hover {
  background: var(--bg-gray);
  transform: translateX(4px);
}

.category-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.category-name {
  font-size: var(--font-sm);
  color: var(--text-primary);
}

.category-count {
  font-size: var(--font-xs);
  color: var(--text-light);
}

.category-icon {
  color: var(--text-light);
  font-size: 12px;
}

/* 标签云 */
.tags-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.tag-item {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: var(--radius-md);
}

.tag-item:hover {
  transform: scale(1.05);
}

.tag-item.large {
  font-size: var(--font-sm);
  padding: var(--spacing-sm) var(--spacing-md);
}

.tag-item.medium {
  font-size: var(--font-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
}

.tag-item.small {
  font-size: 10px;
  padding: 2px var(--spacing-xs);
}

.tag-count {
  margin-left: 2px;
  opacity: 0.7;
}

/* 友情链接 */
.links-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.link-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  text-decoration: none;
  transition: all 0.3s ease;
}

.link-item:hover {
  background: var(--bg-gray);
  transform: translateX(4px);
}

.link-avatar {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.link-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.link-info {
  flex: 1;
  min-width: 0;
}

.link-name {
  display: block;
  font-size: var(--font-sm);
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 2px;
}

.link-desc {
  display: block;
  font-size: var(--font-xs);
  color: var(--text-light);
}

.link-icon {
  color: var(--text-light);
  font-size: 12px;
}

/* 实用工具 */
.tools-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.tool-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
}

.tool-item:hover {
  background: var(--bg-gray);
  transform: translateX(4px);
}

.tool-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.tool-icon .text-blue {
  color: #409eff;
  background: rgba(64, 158, 255, 0.1);
}

.tool-icon .text-green {
  color: #67c23a;
  background: rgba(103, 194, 58, 0.1);
}

.tool-icon .text-orange {
  color: #e6a23c;
  background: rgba(230, 162, 60, 0.1);
}

.tool-icon .text-purple {
  color: #9c27b0;
  background: rgba(156, 39, 176, 0.1);
}

.tool-info {
  flex: 1;
  min-width: 0;
}

.tool-name {
  display: block;
  font-size: var(--font-sm);
  color: var(--text-primary);
  font-weight: 500;
  margin-bottom: 2px;
}

.tool-desc {
  display: block;
  font-size: var(--font-xs);
  color: var(--text-light);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .right-sidebar {
    width: 260px;
  }
}

@media (max-width: 1024px) {
  .right-sidebar {
    display: none;
  }
}
</style>
