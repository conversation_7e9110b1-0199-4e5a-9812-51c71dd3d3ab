package com.personal.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.personal.blog.dto.UserLoginDTO;
import com.personal.blog.dto.UserRegisterDTO;
import com.personal.blog.dto.UserUpdateDTO;
import com.personal.blog.entity.User;
import com.personal.blog.vo.UserVO;

import java.util.Map;

/**
 * 用户Service接口
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
public interface UserService extends IService<User> {

    /**
     * 用户注册
     * 
     * @param userRegisterDTO 注册信息
     * @return 用户信息
     */
    UserVO register(UserRegisterDTO userRegisterDTO);

    /**
     * 用户登录
     * 
     * @param userLoginDTO 登录信息
     * @return 包含token和用户信息的Map
     */
    Map<String, Object> login(UserLoginDTO userLoginDTO);

    /**
     * 用户登出
     */
    void logout();

    /**
     * 获取当前用户信息
     * 
     * @return 用户信息
     */
    UserVO getCurrentUser();

    /**
     * 根据用户名或邮箱查找用户
     * 
     * @param usernameOrEmail 用户名或邮箱
     * @return 用户信息
     */
    User findByUsernameOrEmail(String usernameOrEmail);

    /**
     * 更新用户信息
     * 
     * @param userUpdateDTO 用户更新信息
     * @return 更新后的用户信息
     */
    UserVO updateUserProfile(UserUpdateDTO userUpdateDTO);
}
