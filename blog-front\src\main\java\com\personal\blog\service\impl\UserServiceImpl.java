package com.personal.blog.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.crypto.digest.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.personal.blog.dto.UserLoginDTO;
import com.personal.blog.dto.UserRegisterDTO;
import com.personal.blog.dto.UserUpdateDTO;
import com.personal.blog.entity.User;
import com.personal.blog.exception.BusinessException;
import com.personal.blog.mapper.UserMapper;
import com.personal.blog.service.UserService;
import com.personal.blog.vo.UserVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户Service实现类
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Override
    public UserVO register(UserRegisterDTO userRegisterDTO) {
        // 检查用户名是否已存在
        User existUser = this.getOne(new LambdaQueryWrapper<User>()
                .eq(User::getUsername, userRegisterDTO.getUsername()));
        if (existUser != null) {
            throw new BusinessException("用户名已存在");
        }

        // 检查邮箱是否已存在
        existUser = this.getOne(new LambdaQueryWrapper<User>()
                .eq(User::getEmail, userRegisterDTO.getEmail()));
        if (existUser != null) {
            throw new BusinessException("邮箱已存在");
        }

        // 创建新用户
        User user = new User();
        user.setUsername(userRegisterDTO.getUsername());
        user.setEmail(userRegisterDTO.getEmail());
        user.setPassword(BCrypt.hashpw(userRegisterDTO.getPassword()));
        user.setNickname(userRegisterDTO.getNickname() != null ? 
                userRegisterDTO.getNickname() : userRegisterDTO.getUsername());
        user.setRole("USER");
        user.setStatus(1);

        this.save(user);

        return BeanUtil.copyProperties(user, UserVO.class);
    }

    @Override
    public Map<String, Object> login(UserLoginDTO userLoginDTO) {
        // 查找用户
        User user = findByUsernameOrEmail(userLoginDTO.getUsername());
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 检查用户状态
        if (user.getStatus() == 0) {
            throw new BusinessException("用户已被禁用");
        }

        // 验证密码
        if (!BCrypt.checkpw(userLoginDTO.getPassword(), user.getPassword())) {
            throw new BusinessException("密码错误");
        }

        // 登录并返回token和用户信息
        StpUtil.login(user.getId());
        String token = StpUtil.getTokenValue();
        UserVO userVO = BeanUtil.copyProperties(user, UserVO.class);
        
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        result.put("user", userVO);
        
        return result;
    }

    @Override
    public void logout() {
        StpUtil.logout();
    }

    @Override
    public UserVO getCurrentUser() {
        Long userId = StpUtil.getLoginIdAsLong();
        User user = this.getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        return BeanUtil.copyProperties(user, UserVO.class);
    }

    @Override
    public User findByUsernameOrEmail(String usernameOrEmail) {
        return this.getOne(new LambdaQueryWrapper<User>()
                .eq(User::getUsername, usernameOrEmail)
                .or()
                .eq(User::getEmail, usernameOrEmail));
    }

    @Override
    public UserVO updateUserProfile(UserUpdateDTO userUpdateDTO) {
        Long userId = StpUtil.getLoginIdAsLong();
        User user = this.getById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 检查邮箱是否被其他用户使用
        if (userUpdateDTO.getEmail() != null && !userUpdateDTO.getEmail().equals(user.getEmail())) {
            User existUser = this.getOne(new LambdaQueryWrapper<User>()
                    .eq(User::getEmail, userUpdateDTO.getEmail())
                    .ne(User::getId, userId));
            if (existUser != null) {
                throw new BusinessException("邮箱已被其他用户使用");
            }
        }

        // 更新用户信息
        if (userUpdateDTO.getNickname() != null) {
            user.setNickname(userUpdateDTO.getNickname());
        }
        if (userUpdateDTO.getEmail() != null) {
            user.setEmail(userUpdateDTO.getEmail());
        }
        if (userUpdateDTO.getAvatar() != null) {
            user.setAvatar(userUpdateDTO.getAvatar());
        }

        this.updateById(user);
        return BeanUtil.copyProperties(user, UserVO.class);
    }
}
