package com.personal.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.personal.blog.entity.UserLike;
import com.personal.blog.vo.ArticleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户点赞记录Mapper接口
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Mapper
public interface UserLikeMapper extends BaseMapper<UserLike> {

    /**
     * 查询用户点赞的文章列表
     * 
     * @param page 分页参数
     * @param userId 用户ID
     * @return 文章列表
     */
    IPage<ArticleVO> selectUserLikedArticles(Page<ArticleVO> page, @Param("userId") Long userId);
} 