package com.personal.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.personal.blog.dto.ArticleCreateDTO;
import com.personal.blog.dto.ArticleQueryDTO;
import com.personal.blog.entity.Article;
import com.personal.blog.vo.ArticleVO;
import com.personal.blog.vo.PageResult;

import java.util.List;

/**
 * 文章Service接口
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
public interface ArticleService extends IService<Article> {

    /**
     * 创建文章
     * 
     * @param articleCreateDTO 文章创建信息
     * @return 文章信息
     */
    ArticleVO createArticle(ArticleCreateDTO articleCreateDTO);

    /**
     * 分页查询文章列表
     * 
     * @param queryDTO 查询条件
     * @return 文章列表
     */
    PageResult<ArticleVO> getArticlePage(ArticleQueryDTO queryDTO);

    /**
     * 根据ID查询文章详情
     * 
     * @param id 文章ID
     * @return 文章详情
     */
    ArticleVO getArticleById(Long id);

    /**
     * 更新文章
     * 
     * @param id 文章ID
     * @param articleCreateDTO 文章更新信息
     * @return 文章信息
     */
    ArticleVO updateArticle(Long id, ArticleCreateDTO articleCreateDTO);

    /**
     * 删除文章
     * 
     * @param id 文章ID
     */
    void deleteArticle(Long id);

    /**
     * 增加文章浏览量
     * 
     * @param id 文章ID
     */
    void incrementViewCount(Long id);

    /**
     * 检查用户是否已点赞文章
     * 
     * @param articleId 文章ID
     * @return 是否已点赞
     */
    boolean isLiked(Long articleId);

    /**
     * 检查用户是否已收藏文章
     * 
     * @param articleId 文章ID
     * @return 是否已收藏
     */
    boolean isFavorited(Long articleId);

    /**
     * 获取当前用户的文章列表
     * 
     * @param current 当前页
     * @param size 每页大小
     * @return 文章列表
     */
    PageResult<ArticleVO> getUserArticles(Integer current, Integer size);

    /**
     * 获取作者热门文章
     * 
     * @param authorId 作者ID
     * @param limit 限制数量
     * @return 热门文章列表
     */
    List<ArticleVO> getAuthorPopularArticles(Long authorId, Integer limit);
}
