import { request } from '@/utils/request'

/**
 * 用户相关 API
 */

// 获取用户信息
export const getUserProfile = () => {
  return request.get('/user/profile')
}

// 更新用户信息
export const updateUserProfile = (data) => {
  return request.put('/user/profile', data)
}

// 获取用户文章列表
export const getUserArticles = (params) => {
  return request.get('/user/articles', params)
}

// 获取用户点赞的文章
export const getUserLikedArticles = (params) => {
  return request.get('/user/likes', params)
}

// 获取用户收藏的文章
export const getUserFavoriteArticles = (params) => {
  return request.get('/user/favorites', params)
}

// 获取用户评论
export const getUserComments = (params) => {
  return request.get('/user/comments', params)
}

// 删除用户评论
export const deleteUserComment = (id) => {
  return request.delete(`/user/comments/${id}`)
}

// 取消收藏文章
export const removeFavorite = (articleId) => {
  return request.delete(`/user/favorites/${articleId}`)
}

// 取消点赞文章
export const removeLike = (articleId) => {
  return request.delete(`/user/likes/${articleId}`)
} 