package com.personal.blog.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * 文件上传Service接口
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
public interface FileService {

    /**
     * 上传头像
     * 
     * @param file 头像文件
     * @return 上传结果
     */
    Map<String, String> uploadAvatar(MultipartFile file);

    /**
     * 上传文章图片
     * 
     * @param file 图片文件
     * @return 上传结果
     */
    Map<String, String> uploadArticleImage(MultipartFile file);

    /**
     * 上传通用文件
     * 
     * @param file 文件
     * @return 上传结果
     */
    Map<String, String> uploadFile(MultipartFile file);

    /**
     * 删除文件
     * 
     * @param fileUrl 文件URL
     * @return 是否删除成功
     */
    boolean deleteFile(String fileUrl);
} 