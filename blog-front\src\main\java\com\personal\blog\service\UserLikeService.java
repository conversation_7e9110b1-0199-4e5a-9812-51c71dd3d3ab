package com.personal.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.personal.blog.entity.UserLike;
import com.personal.blog.vo.ArticleVO;
import com.personal.blog.vo.PageResult;

/**
 * 用户点赞Service接口
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
public interface UserLikeService extends IService<UserLike> {

    /**
     * 点赞文章
     * 
     * @param articleId 文章ID
     * @return 是否点赞成功
     */
    boolean likeArticle(Long articleId);

    /**
     * 取消点赞
     * 
     * @param articleId 文章ID
     * @return 是否取消成功
     */
    boolean unlikeArticle(Long articleId);

    /**
     * 检查用户是否已点赞
     * 
     * @param articleId 文章ID
     * @return 是否已点赞
     */
    boolean isLiked(Long articleId);

    /**
     * 获取用户点赞的文章列表
     * 
     * @param current 当前页
     * @param size 每页大小
     * @return 文章列表
     */
    PageResult<ArticleVO> getUserLikedArticles(Integer current, Integer size);

    /**
     * 取消点赞文章
     * 
     * @param articleId 文章ID
     */
    void removeLike(Long articleId);
} 