package com.personal.blog.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 关于页面信息服务测试
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@SpringBootTest
public class AboutInfoServiceTest {

    @Autowired
    private AboutInfoService aboutInfoService;

    @Test
    public void testGetAboutInfo() {
        Map<String, Object> aboutInfo = aboutInfoService.getAboutInfo();
        
        if (aboutInfo != null) {
            System.out.println("获取到的关于页面信息:");
            System.out.println("姓名: " + aboutInfo.get("name"));
            System.out.println("标题: " + aboutInfo.get("title"));
            System.out.println("头像: " + aboutInfo.get("avatar"));
            System.out.println("描述: " + aboutInfo.get("description"));
            System.out.println("联系方式: " + aboutInfo.get("contact"));
            System.out.println("社交链接: " + aboutInfo.get("social"));
            System.out.println("前端技能: " + aboutInfo.get("skillsFrontend"));
            System.out.println("后端技能: " + aboutInfo.get("skillsBackend"));
            System.out.println("数据库技能: " + aboutInfo.get("skillsDatabase"));
            System.out.println("工具技能: " + aboutInfo.get("skillsTools"));
        } else {
            System.out.println("没有获取到关于页面信息");
        }
        
        // 暂时不进行断言，只是打印信息
    }
} 