<template>
  <aside class="left-sidebar">
    <!-- 个人简介卡片 -->
    <div class="profile-card">
      <div class="profile-header">
        <div class="avatar-container">
          <img :src="userStore.avatar" :alt="userStore.nickname" class="avatar" />
          <div class="avatar-ring"></div>
        </div>
        <div class="profile-info">
          <h3 class="nickname">{{ userStore.nickname }}</h3>
          <p class="title">全栈开发工程师</p>
        </div>
      </div>
      <div class="profile-stats">
        <div class="stat-item">
          <span class="stat-number">{{ stats.articles }}</span>
          <span class="stat-label">文章</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ stats.views }}</span>
          <span class="stat-label">访问</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ stats.likes }}</span>
          <span class="stat-label">点赞</span>
        </div>
      </div>
      <div class="profile-bio">
        <p>热爱技术，专注于前后端开发，分享编程经验与技术见解。</p>
      </div>
    </div>

    <!-- 技能标签云 -->
    <div class="skills-card">
      <div class="card-header">
        <h4 class="card-title">
          <el-icon><Tools /></el-icon>
          技术栈
        </h4>
      </div>
      <div class="skills-cloud">
        <el-tag 
          v-for="skill in skills" 
          :key="skill.name"
          :type="skill.type"
          :effect="skill.effect"
          class="skill-tag"
          :class="skill.level"
        >
          {{ skill.name }}
        </el-tag>
      </div>
    </div>

    <!-- GitHub统计 -->
    <div class="github-card">
      <div class="card-header">
        <h4 class="card-title">
          <el-icon><Link /></el-icon>
          GitHub
        </h4>
      </div>
      <div class="github-stats">
        <div class="github-item">
          <el-icon class="github-icon"><Star /></el-icon>
          <span class="github-text">{{ githubStats.stars }} Stars</span>
        </div>
        <div class="github-item">
          <el-icon class="github-icon"><Connection /></el-icon>
          <span class="github-text">{{ githubStats.repos }} Repos</span>
        </div>
        <div class="github-item">
          <el-icon class="github-icon"><User /></el-icon>
          <span class="github-text">{{ githubStats.followers }} Followers</span>
        </div>
      </div>
      <a href="https://github.com" target="_blank" class="github-link">
        <el-button type="primary" size="small" class="github-btn">
          <el-icon><Link /></el-icon>
          访问 GitHub
        </el-button>
      </a>
    </div>

    <!-- 最近动态 -->
    <div class="activity-card">
      <div class="card-header">
        <h4 class="card-title">
          <el-icon><Clock /></el-icon>
          最近动态
        </h4>
      </div>
      <div class="activity-list">
        <div 
          v-for="activity in activities" 
          :key="activity.id"
          class="activity-item"
        >
          <div class="activity-icon">
            <el-icon :class="activity.iconClass">
              <component :is="activity.icon" />
            </el-icon>
          </div>
          <div class="activity-content">
            <p class="activity-text">{{ activity.text }}</p>
            <span class="activity-time">{{ formatTime(activity.time) }}</span>
          </div>
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { useArticleStore } from '@/store/article'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

const userStore = useUserStore()
const articleStore = useArticleStore()

// 统计数据
const stats = ref({
  articles: 0,
  views: 0,
  likes: 0
})

// 技能标签
const skills = ref([
  { name: 'Vue.js', type: 'success', effect: 'dark', level: 'expert' },
  { name: 'React', type: 'info', effect: 'dark', level: 'advanced' },
  { name: 'JavaScript', type: 'warning', effect: 'dark', level: 'expert' },
  { name: 'TypeScript', type: 'primary', effect: 'dark', level: 'advanced' },
  { name: 'Node.js', type: 'success', effect: 'plain', level: 'advanced' },
  { name: 'Spring Boot', type: 'success', effect: 'plain', level: 'expert' },
  { name: 'MySQL', type: 'info', effect: 'plain', level: 'advanced' },
  { name: 'Redis', type: 'danger', effect: 'plain', level: 'intermediate' },
  { name: 'Docker', type: 'primary', effect: 'plain', level: 'intermediate' },
  { name: 'Git', type: 'warning', effect: 'plain', level: 'expert' }
])

// GitHub统计
const githubStats = ref({
  stars: 128,
  repos: 42,
  followers: 256
})

// 最近动态
const activities = ref([
  {
    id: 1,
    icon: 'Document',
    iconClass: 'text-blue',
    text: '发布了新文章《Vue 3 Composition API 实践指南》',
    time: new Date(Date.now() - 2 * 60 * 60 * 1000)
  },
  {
    id: 2,
    icon: 'Star',
    iconClass: 'text-yellow',
    text: '收到了来自读者的点赞和评论',
    time: new Date(Date.now() - 5 * 60 * 60 * 1000)
  },
  {
    id: 3,
    icon: 'Edit',
    iconClass: 'text-green',
    text: '更新了《Spring Boot 微服务架构》文章',
    time: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
  },
  {
    id: 4,
    icon: 'Connection',
    iconClass: 'text-purple',
    text: '在 GitHub 上提交了新的开源项目',
    time: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
  }
])

// 格式化时间
const formatTime = (time) => {
  return dayjs(time).fromNow()
}

// 初始化数据
onMounted(async () => {
  // 模拟获取统计数据
  stats.value = {
    articles: articleStore.articles.length || 24,
    views: 12580,
    likes: 1024
  }
})
</script>

<style scoped>
.left-sidebar {
  width: 280px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

/* 个人简介卡片 */
.profile-card {
  background: var(--bg-white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
}

.profile-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.profile-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.avatar-container {
  position: relative;
  margin-bottom: var(--spacing-md);
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--primary-light);
  transition: all 0.3s ease;
}

.avatar-ring {
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 2px solid transparent;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  background-clip: padding-box;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.profile-card:hover .avatar-ring {
  opacity: 1;
}

.nickname {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.title {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  margin: 0;
}

.profile-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md) 0;
  border-top: 1px solid var(--border-light);
  border-bottom: 1px solid var(--border-light);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.stat-number {
  font-size: var(--font-lg);
  font-weight: 700;
  color: var(--primary-color);
}

.stat-label {
  font-size: var(--font-xs);
  color: var(--text-light);
}

.profile-bio {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.6;
  text-align: center;
}

/* 卡片通用样式 */
.skills-card,
.github-card,
.activity-card {
  background: var(--bg-white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
}

.skills-card:hover,
.github-card:hover,
.activity-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  margin-bottom: var(--spacing-md);
}

.card-title {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0;
}

/* 技能标签云 */
.skills-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.skill-tag {
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: var(--font-xs);
  border-radius: var(--radius-md);
}

.skill-tag:hover {
  transform: scale(1.05);
}

.skill-tag.expert {
  font-weight: 600;
}

.skill-tag.advanced {
  font-weight: 500;
}

.skill-tag.intermediate {
  font-weight: 400;
}

/* GitHub统计 */
.github-stats {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.github-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: background-color 0.3s ease;
}

.github-item:hover {
  background: var(--bg-gray);
}

.github-icon {
  color: var(--primary-color);
  font-size: 16px;
}

.github-text {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.github-link {
  display: block;
  text-decoration: none;
}

.github-btn {
  width: 100%;
  border-radius: var(--radius-md);
}

/* 最近动态 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.activity-item {
  display: flex;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: background-color 0.3s ease;
}

.activity-item:hover {
  background: var(--bg-gray);
}

.activity-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.activity-icon .text-blue {
  color: #409eff;
  background: rgba(64, 158, 255, 0.1);
}

.activity-icon .text-yellow {
  color: #e6a23c;
  background: rgba(230, 162, 60, 0.1);
}

.activity-icon .text-green {
  color: #67c23a;
  background: rgba(103, 194, 58, 0.1);
}

.activity-icon .text-purple {
  color: #9c27b0;
  background: rgba(156, 39, 176, 0.1);
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-text {
  font-size: var(--font-sm);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  line-height: 1.4;
}

.activity-time {
  font-size: var(--font-xs);
  color: var(--text-light);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-sidebar {
    width: 260px;
  }
}

@media (max-width: 1024px) {
  .left-sidebar {
    display: none;
  }
}
</style>
