package com.personal.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.personal.blog.dto.AboutUpdateDTO;
import com.personal.blog.entity.AboutInfo;
import com.personal.blog.entity.User;
import com.personal.blog.mapper.AboutInfoMapper;
import com.personal.blog.mapper.UserMapper;
import com.personal.blog.service.AboutInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 关于页面信息 Service 实现类
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AboutInfoServiceImpl implements AboutInfoService {

    private final AboutInfoMapper aboutInfoMapper;
    private final UserMapper userMapper;

    @Override
    public Map<String, Object> getAboutInfo() {
        AboutInfo aboutInfo = getAboutInfoEntity();
        if (aboutInfo == null) {
            return null;
        }

        // 获取用户信息
        User user = userMapper.selectById(aboutInfo.getUserId());
        if (user == null) {
            return null;
        }

        Map<String, Object> result = new HashMap<>();
        result.put("name", user.getNickname() != null ? user.getNickname() : user.getUsername());
        result.put("title", aboutInfo.getTitle());
        result.put("avatar", user.getAvatar() != null ? user.getAvatar() : "/default-avatar.png");
        result.put("description", aboutInfo.getDescription());

        // 联系方式
        Map<String, String> contact = new HashMap<>();
        contact.put("email", aboutInfo.getContactEmail());
        contact.put("github", aboutInfo.getContactGithub());
        contact.put("location", aboutInfo.getContactLocation());
        result.put("contact", contact);

        // 社交链接
        result.put("social", aboutInfo.getSocialLinks());

        // 技能信息
        result.put("skillsFrontend", aboutInfo.getSkillsFrontend());
        result.put("skillsBackend", aboutInfo.getSkillsBackend());
        result.put("skillsDatabase", aboutInfo.getSkillsDatabase());
        result.put("skillsTools", aboutInfo.getSkillsTools());

        return result;
    }

    @Override
    @Transactional
    public Map<String, Object> updateAboutInfo(AboutUpdateDTO aboutUpdateDTO) {
        AboutInfo aboutInfo = getAboutInfoEntity();
        
        if (aboutInfo == null) {
            // 如果不存在，创建新的记录（默认关联管理员用户）
            aboutInfo = new AboutInfo();
            aboutInfo.setUserId(1L); // 默认关联管理员用户
        }

        // 更新基本信息
        if (aboutUpdateDTO.getTitle() != null) {
            aboutInfo.setTitle(aboutUpdateDTO.getTitle());
        }
        if (aboutUpdateDTO.getDescription() != null) {
            aboutInfo.setDescription(aboutUpdateDTO.getDescription());
        }

        // 更新联系方式
        if (aboutUpdateDTO.getContact() != null) {
            if (aboutUpdateDTO.getContact().getEmail() != null) {
                aboutInfo.setContactEmail(aboutUpdateDTO.getContact().getEmail());
            }
            if (aboutUpdateDTO.getContact().getGithub() != null) {
                aboutInfo.setContactGithub(aboutUpdateDTO.getContact().getGithub());
            }
            if (aboutUpdateDTO.getContact().getLocation() != null) {
                aboutInfo.setContactLocation(aboutUpdateDTO.getContact().getLocation());
            }
        }

        // 更新社交链接
        if (aboutUpdateDTO.getSocial() != null) {
            // 将 SocialLink 对象转换为 Map
            List<Map<String, Object>> socialLinks = aboutUpdateDTO.getSocial().stream()
                .map(socialLink -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("name", socialLink.getName());
                    map.put("icon", socialLink.getIcon());
                    map.put("url", socialLink.getUrl());
                    return map;
                })
                .collect(java.util.stream.Collectors.toList());
            aboutInfo.setSocialLinks(socialLinks);
        }

        // 保存或更新
        if (aboutInfo.getId() == null) {
            aboutInfoMapper.insert(aboutInfo);
        } else {
            aboutInfoMapper.updateById(aboutInfo);
        }

        return getAboutInfo();
    }

    @Override
    public AboutInfo getAboutInfoEntity() {
        // 使用原生SQL映射来确保JsonTypeHandler被正确调用
        AboutInfo aboutInfo = aboutInfoMapper.selectAboutInfoWithMapping();
        
        // 如果原生SQL映射失败，回退到MyBatis-Plus的自动映射
        if (aboutInfo == null) {
            LambdaQueryWrapper<AboutInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.last("LIMIT 1");
            aboutInfo = aboutInfoMapper.selectOne(wrapper);
        }
        
        return aboutInfo;
    }
} 