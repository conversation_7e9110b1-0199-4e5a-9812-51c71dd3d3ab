package com.personal.blog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 评论信息VO
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Data
@Schema(description = "评论信息")
public class CommentVO {

    @Schema(description = "评论ID")
    private Long id;

    @Schema(description = "文章ID")
    private Long articleId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户昵称")
    private String userNickname;

    @Schema(description = "用户头像")
    private String userAvatar;

    @Schema(description = "父评论ID（第一层评论ID）")
    private Long parentId;

    @Schema(description = "回复目标评论ID（第二层回复的目标）")
    private Long replyToId;

    @Schema(description = "评论内容")
    private String content;

    @Schema(description = "IP地址")
    private String ipAddress;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "回复的用户ID")
    private Long replyUserId;

    @Schema(description = "回复的用户昵称")
    private String replyUserNickname;

    @Schema(description = "回复的用户头像")
    private String replyUserAvatar;

    @Schema(description = "回复的用户信息")
    private CommentVO replyTo;

    @Schema(description = "回复链文本")
    private String replyChain;

    @Schema(description = "点赞数")
    private Integer likeCount;

    @Schema(description = "当前用户是否已点赞")
    private Boolean isLiked;

    @Schema(description = "子评论列表")
    private List<CommentVO> children;

    @Schema(description = "文章标题")
    private String articleTitle;
}
