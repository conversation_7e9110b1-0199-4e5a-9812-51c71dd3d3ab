package com.personal.blog.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;

/**
 * 用户更新DTO
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Data
@Schema(description = "用户更新DTO")
public class UserUpdateDTO {

    @Schema(description = "昵称")
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;

    @Schema(description = "邮箱")
    @Email(message = "邮箱格式不正确")
    private String email;

    @Schema(description = "头像")
    private String avatar;
} 