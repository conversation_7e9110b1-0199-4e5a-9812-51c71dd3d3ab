<template>
  <article class="article-card" @click="handleClick">
    <!-- 封面图片 -->
    <div class="article-cover" v-if="article.coverImage">
      <img :src="article.coverImage" :alt="article.title" />
      <div class="cover-overlay">
        <el-icon class="read-icon"><View /></el-icon>
      </div>
    </div>

    <!-- 文章内容 -->
    <div class="article-content">
      <!-- 分类标签 -->
      <div class="article-category" v-if="article.category">
        <el-tag :type="getCategoryType(article.category.id)" size="small">
          {{ article.category.name }}
        </el-tag>
      </div>

      <!-- 文章标题 -->
      <h3 class="article-title">{{ article.title }}</h3>

      <!-- 文章摘要 -->
      <p class="article-summary" v-if="article.summary">
        {{ article.summary }}
      </p>

      <!-- 文章元信息 -->
      <div class="article-meta">
        <div class="meta-left">
          <span class="author">
            <el-icon><User /></el-icon>
            {{ article.authorNickname || '匿名' }}
          </span>
          <span class="date">
            <el-icon><Calendar /></el-icon>
            {{ formatDate(article.createTime) }}
          </span>
        </div>
        <div class="meta-right">
          <span class="view-count">
            <el-icon><View /></el-icon>
            {{ article.viewCount || 0 }}
          </span>
          <span class="comment-count">
            <el-icon><ChatDotRound /></el-icon>
            {{ article.commentCount || 0 }}
          </span>
          <span class="like-count" v-if="article.likeCount">
            <el-icon><Star /></el-icon>
            {{ article.likeCount }}
          </span>
        </div>
      </div>

      <!-- 文章标签 -->
      <div class="article-tags" v-if="article.tags && article.tags.length">
        <el-tag 
          v-for="tag in article.tags.slice(0, 3)" 
          :key="tag.id"
          size="small"
          effect="plain"
          class="tag-item"
        >
          {{ tag.name }}
        </el-tag>
        <span v-if="article.tags.length > 3" class="more-tags">
          +{{ article.tags.length - 3 }}
        </span>
      </div>
    </div>

    <!-- 置顶标识 -->
    <div class="top-badge" v-if="article.isTop">
      <el-icon><Top /></el-icon>
      置顶
    </div>
  </article>
</template>

<script setup>
import { computed } from 'vue'
import dayjs from 'dayjs'

const props = defineProps({
  article: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['click'])

// 处理点击事件
const handleClick = () => {
  emit('click', props.article)
}

// 格式化日期
const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD')
}

// 获取分类类型（用于标签颜色）
const getCategoryType = (categoryId) => {
  const types = ['', 'success', 'info', 'warning', 'danger']
  return types[categoryId % types.length]
}
</script>

<style scoped>
.article-card {
  background: var(--bg-white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-light);
}

.article-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
  transform: scaleX(0);
  transition: transform 0.3s ease;
  z-index: 1;
}

.article-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-8px);
  border-color: var(--primary-light);
}

.article-card:hover::before {
  transform: scaleX(1);
}

.article-cover {
  position: relative;
  height: 220px;
  overflow: hidden;
  background: linear-gradient(135deg, var(--bg-gray) 0%, var(--border-light) 100%);
}

.article-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.article-card:hover .article-cover img {
  transform: scale(1.08);
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(44, 62, 80, 0.6) 0%, rgba(52, 73, 94, 0.6) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.article-card:hover .cover-overlay {
  opacity: 1;
}

.read-icon {
  color: white;
  font-size: 28px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.article-content {
  padding: var(--spacing-lg);
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.article-category {
  margin-bottom: var(--spacing-xs);
}

.article-title {
  font-size: var(--font-lg);
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1.4;
  margin-bottom: var(--spacing-sm);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.3s ease;
}

.article-card:hover .article-title {
  color: var(--accent-color);
}

.article-summary {
  color: var(--text-secondary);
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
  margin-bottom: var(--spacing-md);
  font-size: var(--font-sm);
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-sm);
  color: var(--text-secondary);
  padding-top: var(--spacing-sm);
  border-top: 1px solid var(--border-light);
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.meta-left,
.meta-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.meta-left span,
.meta-right span {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}

.article-tags {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
  margin-top: var(--spacing-sm);
}

.tag-item {
  font-size: var(--font-xs);
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
}

.tag-item:hover {
  transform: translateY(-1px);
}

.more-tags {
  font-size: var(--font-xs);
  color: var(--text-secondary);
  background: var(--bg-gray);
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-weight: 500;
}

.top-badge {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background: linear-gradient(45deg, var(--accent-color), var(--accent-light));
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-xs);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(230, 126, 34, 0.3);
  backdrop-filter: blur(10px);
}

/* 移动端样式 */
@media (max-width: 768px) {
  .article-cover {
    height: 160px;
  }

  .article-content {
    padding: var(--spacing-md);
  }

  .article-title {
    font-size: var(--font-md);
  }

  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .meta-left,
  .meta-right {
    gap: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .article-cover {
    height: 140px;
  }

  .article-content {
    padding: var(--spacing-sm);
  }

  .article-summary {
    -webkit-line-clamp: 2;
  }

  .article-meta {
    font-size: var(--font-xs);
  }

  .meta-left,
  .meta-right {
    gap: var(--spacing-xs);
  }
}
</style>
