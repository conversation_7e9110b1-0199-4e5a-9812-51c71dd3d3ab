<template>
  <div class="main-layout">
    <!-- 头部导航 - 100%宽度 -->
    <Header class="layout-header" />

    <!-- 主体内容区域 - 90%宽度 -->
    <main class="main-content">
      <div class="content-container">
        <!-- 三栏布局 -->
        <div class="content-wrapper">
          <!-- 左侧边栏 -->
          <aside class="left-sidebar" v-if="showLeftSidebar">
            <LeftSidebar v-if="isHomePage" />
            <div v-else class="sidebar-placeholder">
              <p>左侧边栏</p>
            </div>
          </aside>

          <!-- 主要内容区域 -->
          <div class="main-content-area" :class="{ 'full-width': !showSidebars }">
            <router-view />
          </div>

          <!-- 右侧边栏 -->
          <aside class="right-sidebar" v-if="showRightSidebar">
            <RightSidebar v-if="isHomePage" />
            <Sidebar v-else-if="isArticlePage" />
            <div v-else class="sidebar-placeholder">
              <p>右侧边栏</p>
            </div>
          </aside>
        </div>
      </div>
    </main>

    <!-- 底部 - 100%宽度 -->
    <Footer class="layout-footer" />

    <!-- 返回顶部按钮 -->
    <BackToTop />
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import Header from './Header.vue'
import Footer from './Footer.vue'
import Sidebar from './Sidebar.vue'
import LeftSidebar from './LeftSidebar.vue'
import RightSidebar from './RightSidebar.vue'
import BackToTop from '@/components/common/BackToTop.vue'
import { useUserStore } from '@/store/user'
import { useAppStore } from '@/store/app'

const route = useRoute()
const userStore = useUserStore()
const appStore = useAppStore()

// 判断当前页面类型
const isHomePage = computed(() => route.name === 'Home')
const isArticlePage = computed(() => route.name === 'ArticleDetail')

// 控制侧边栏显示
const showLeftSidebar = computed(() => isHomePage.value)
const showRightSidebar = computed(() => isHomePage.value || isArticlePage.value)
const showSidebars = computed(() => showLeftSidebar.value || showRightSidebar.value)

onMounted(async () => {
  // 初始化应用设置
  if (appStore.initApp) {
    appStore.initApp()
  }

  // 初始化用户信息
  await userStore.initUser()
})
</script>

<style scoped>
.main-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 头部 - 100%宽度 */
.layout-header {
  width: 100%;
  position: sticky;
  top: 0;
  z-index: 1000;
}

/* 主体内容 - 90%宽度 */
.main-content {
  flex: 1;
  width: 100%;
  padding: var(--spacing-lg) 0;
}

.content-container {
  width: 90%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* 三栏布局 */
.content-wrapper {
  display: grid;
  grid-template-columns: 280px 1fr 280px;
  gap: var(--spacing-xl);
  align-items: start;
  min-height: 500px;
}

/* 侧边栏样式 */
.left-sidebar,
.right-sidebar {
  position: sticky;
  top: calc(64px + var(--spacing-lg)); /* Header高度 + 间距 */
  height: fit-content;
}

.main-content-area {
  min-width: 0; /* 防止内容溢出 */
  width: 100%;
}

.main-content-area.full-width {
  grid-column: 1 / -1; /* 占据整个网格宽度 */
}

/* 侧边栏占位符样式 */
.sidebar-placeholder {
  background: var(--bg-white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  text-align: center;
  color: var(--text-light);
  font-size: var(--font-sm);
}

/* 底部 - 100%宽度 */
.layout-footer {
  width: 100%;
  margin-top: auto;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-container {
    width: 95%;
  }

  .content-wrapper {
    grid-template-columns: 260px 1fr 260px;
    gap: var(--spacing-lg);
  }
}

@media (max-width: 1024px) {
  .content-wrapper {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .left-sidebar,
  .right-sidebar {
    display: none;
  }
}

@media (max-width: 768px) {
  .content-container {
    width: 100%;
    padding: 0 var(--spacing-sm);
  }

  .main-content {
    padding: var(--spacing-md) 0;
  }
}

@media (max-width: 480px) {
  .content-container {
    padding: 0 var(--spacing-xs);
  }

  .main-content {
    padding: var(--spacing-sm) 0;
  }
}
</style>
