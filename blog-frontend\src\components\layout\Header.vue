<template>
  <header class="blog-header">
    <div class="container">
      <div class="header-content">
        <!-- Logo -->
        <div class="logo" @click="$router.push('/')">
          <h1>个人博客</h1>
        </div>

        <!-- 导航菜单 -->
        <nav class="nav-menu" :class="{ 'mobile-open': mobileMenuOpen }">
          <router-link to="/" class="nav-item" @click="closeMobileMenu">首页</router-link>
          <router-link to="/category" class="nav-item" @click="closeMobileMenu">分类</router-link>
          <router-link to="/about" class="nav-item" @click="closeMobileMenu">关于</router-link>
        </nav>

        <!-- 搜索框 -->
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索文章..."
            class="search-input"
            @keyup.enter="handleSearch"
          >
            <template #suffix>
              <el-icon class="search-icon" @click="handleSearch">
                <Search />
              </el-icon>
            </template>
          </el-input>
        </div>

        <!-- 用户区域 -->
        <div class="user-area">
          <template v-if="userStore.isLoggedIn">
            <el-dropdown @command="handleUserCommand">
              <div class="user-info">
                <img :src="getAvatarUrl(userStore.avatar)" :alt="userStore.nickname" class="user-avatar">
                <span class="user-name">{{ userStore.nickname }}</span>
                <el-icon><ArrowDown /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                  <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
          <template v-else>
            <el-button type="primary" @click="$router.push('/login')">登录</el-button>
          </template>
        </div>

        <!-- 移动端菜单按钮 -->
        <div class="mobile-menu-btn" @click="toggleMobileMenu">
          <el-icon><Menu /></el-icon>
        </div>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/user'
import { ElMessage } from 'element-plus'
import { getAvatarUrl } from '@/utils/avatar'

const router = useRouter()
const userStore = useUserStore()

const searchKeyword = ref('')
const mobileMenuOpen = ref(false)

// 搜索处理
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  router.push({
    path: '/search',
    query: { keyword: searchKeyword.value.trim() }
  })
  searchKeyword.value = ''
}

// 用户下拉菜单处理
const handleUserCommand = (command) => {
  switch (command) {
    case 'profile':
      router.push('/user')
      break
    case 'logout':
      userStore.logoutUser()
      ElMessage.success('退出登录成功')
      router.push('/')
      break
  }
}

// 移动端菜单切换
const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

// 关闭移动端菜单
const closeMobileMenu = () => {
  mobileMenuOpen.value = false
}
</script>

<style scoped>
.blog-header {
  background: var(--bg-white);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  gap: var(--spacing-lg);
}

.logo {
  cursor: pointer;
  transition: color 0.3s ease;
}

.logo:hover {
  color: var(--accent-color);
}

.logo h1 {
  font-size: var(--font-xl);
  font-weight: 600;
  color: var(--primary-color);
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.nav-item {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
  font-weight: 500;
}

.nav-item:hover,
.nav-item.router-link-active {
  background: var(--accent-color);
  color: white;
}

.search-box {
  flex: 1;
  max-width: 300px;
}

.search-input {
  width: 100%;
}

.search-icon {
  cursor: pointer;
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.search-icon:hover {
  color: var(--accent-color);
}

.user-area {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: background 0.3s ease;
}

.user-info:hover {
  background: var(--bg-gray);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-weight: 500;
  color: var(--text-primary);
}

.mobile-menu-btn {
  display: none;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: background 0.3s ease;
}

.mobile-menu-btn:hover {
  background: var(--bg-gray);
}

/* 移动端样式 */
@media (max-width: 768px) {
  .header-content {
    gap: var(--spacing-md);
  }

  .nav-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-white);
    flex-direction: column;
    padding: var(--spacing-md);
    box-shadow: var(--shadow-md);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .nav-menu.mobile-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .search-box {
    max-width: 200px;
  }

  .mobile-menu-btn {
    display: block;
  }

  .user-name {
    display: none;
  }
}

@media (max-width: 480px) {
  .logo h1 {
    font-size: var(--font-lg);
  }

  .search-box {
    max-width: 150px;
  }
}
</style>
