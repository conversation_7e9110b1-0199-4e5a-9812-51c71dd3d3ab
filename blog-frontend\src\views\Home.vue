<template>
  <div class="home-page">
    <!-- Hero区域 -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="hero-pattern"></div>
        <div class="hero-overlay"></div>
        <!-- 装饰性元素 -->
        <div class="hero-decorations">
          <div class="floating-icon icon-1">
            <el-icon><Code /></el-icon>
          </div>
          <div class="floating-icon icon-2">
            <el-icon><Monitor /></el-icon>
          </div>
          <div class="floating-icon icon-3">
            <el-icon><Connection /></el-icon>
          </div>
          <div class="floating-icon icon-4">
            <el-icon><DataAnalysis /></el-icon>
          </div>
          <div class="floating-icon icon-5">
            <el-icon><Cpu /></el-icon>
          </div>
          <div class="floating-icon icon-6">
            <el-icon><Server /></el-icon>
          </div>
        </div>
      </div>
      <div class="hero-container">
        <div class="hero-content">
          <div class="hero-badge">
            <el-icon><Star /></el-icon>
            个人技术博客
          </div>
          <h1 class="hero-title">
            <span class="title-line">欢迎来到我的博客</span>
            <span class="title-accent">探索技术的无限可能</span>
          </h1>
          <p class="hero-subtitle">
            分享技术见解，记录学习历程，探索编程世界的奥秘
          </p>
          <div class="hero-actions">
            <el-button 
              type="primary" 
              size="large" 
              class="hero-btn primary"
              @click="$router.push('/about')"
            >
              <el-icon><InfoFilled /></el-icon>
              了解更多
            </el-button>
            <el-button 
              size="large" 
              class="hero-btn secondary"
              @click="scrollToArticles"
            >
              <el-icon><Document /></el-icon>
              浏览文章
            </el-button>
          </div>
        </div>
        <div class="hero-visual">
          <div class="visual-grid">
            <div class="feature-card">
              <div class="card-icon">
                <el-icon><Code /></el-icon>
              </div>
              <div class="card-content">
                <h4>技术分享</h4>
                <p>最新技术动态与见解</p>
              </div>
            </div>
            <div class="feature-card">
              <div class="card-icon">
                <el-icon><Lightbulb /></el-icon>
              </div>
              <div class="card-content">
                <h4>学习笔记</h4>
                <p>个人学习心得总结</p>
              </div>
            </div>
            <div class="feature-card">
              <div class="card-icon">
                <el-icon><Tools /></el-icon>
              </div>
              <div class="card-content">
                <h4>实用工具</h4>
                <p>开发效率提升技巧</p>
              </div>
            </div>
            <div class="feature-card">
              <div class="card-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="card-content">
                <h4>行业趋势</h4>
                <p>技术发展趋势分析</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 文章区域 -->
    <section class="articles-section" ref="articlesRef">
      <div class="section-background">
        <div class="bg-pattern"></div>
        <div class="bg-decoration left"></div>
        <div class="bg-decoration right"></div>
      </div>
      <div class="section-container">
        <!-- 三栏布局容器 -->
        <div class="main-content-layout">
          <!-- 左侧边栏 -->
          <LeftSidebar class="left-sidebar-wrapper" />

          <!-- 中间主要内容区域 -->
          <div class="main-content-area">
            <div class="section-header">
              <div class="header-content">
                <h2 class="section-title">
                  <span class="title-icon">
                    <el-icon><Document /></el-icon>
                  </span>
                  最新文章
                </h2>
                <p class="section-subtitle">发现最新的技术文章和见解</p>
              </div>
              <div class="section-actions">
                <el-button-group class="sort-buttons">
                  <el-button
                    :type="sortBy === 'latest' ? 'primary' : ''"
                    :class="{ active: sortBy === 'latest' }"
                    @click="changeSortBy('latest')"
                  >
                    <el-icon><Clock /></el-icon>
                    最新
                  </el-button>
                  <el-button
                    :type="sortBy === 'hot' ? 'primary' : ''"
                    :class="{ active: sortBy === 'hot' }"
                    @click="changeSortBy('hot')"
                  >
                    <el-icon><Fire /></el-icon>
                    热门
                  </el-button>
                </el-button-group>
              </div>
            </div>

            <!-- 文章网格 -->
            <div class="articles-grid" v-loading="articleStore.loading">
              <ArticleCard
                v-for="article in articleStore.articles"
                :key="article.id"
                :article="article"
                @click="$router.push(`/article/${article.id}`)"
              />
            </div>

            <!-- 空状态 -->
            <div v-if="!articleStore.articles.length && !articleStore.loading" class="empty-state">
              <div class="empty-content">
                <el-icon class="empty-icon"><Document /></el-icon>
                <h3>暂无文章</h3>
                <p>还没有发布任何文章，敬请期待</p>
              </div>
            </div>

            <!-- 加载更多 -->
            <div class="load-more" v-if="articleStore.hasMore">
              <el-button
                :loading="articleStore.loading"
                @click="loadMore"
                size="large"
                class="load-more-btn"
              >
                <el-icon><Refresh /></el-icon>
                加载更多
              </el-button>
            </div>
          </div>

          <!-- 右侧边栏 -->
          <RightSidebar class="right-sidebar-wrapper" />
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useArticleStore } from '@/store/article'
import ArticleCard from '@/components/article/ArticleCard.vue'
import LeftSidebar from '@/components/layout/LeftSidebar.vue'
import RightSidebar from '@/components/layout/RightSidebar.vue'

const articleStore = useArticleStore()
const articlesRef = ref()
const sortBy = ref('latest')

// 切换排序方式
const changeSortBy = async (type) => {
  if (sortBy.value === type) return

  sortBy.value = type
  articleStore.clearArticles()

  const params = type === 'hot' ? { sortBy: 'view_count', sortOrder: 'desc' } : { sortBy: 'create_time', sortOrder: 'desc' }
  await articleStore.fetchArticles(params)
}

// 滚动到文章区域
const scrollToArticles = () => {
  articlesRef.value?.scrollIntoView({ 
    behavior: 'smooth',
    block: 'start'
  })
}

// 加载更多文章
const loadMore = async () => {
  const params = sortBy.value === 'hot' ? { sortBy: 'view_count', sortOrder: 'desc' } : { sortBy: 'create_time', sortOrder: 'desc' }
  await articleStore.loadMoreArticles(params)
}

// 初始化数据
onMounted(async () => {
  // 如果已有数据则不重复加载
  if (articleStore.articles.length === 0) {
    await articleStore.fetchArticles({ sortBy: 'create_time', sortOrder: 'desc' })
  }
})
</script>

<style scoped>
.home-page {
  min-height: 100vh;
  background: var(--bg-color);
}

/* Hero区域样式 */
.hero-section {
  position: relative;
  min-height: 500px;
  display: flex;
  align-items: center;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255,255,255,0.1) 0%, transparent 50%);
  animation: float 6s ease-in-out infinite;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
}

/* 装饰性元素 */
.hero-decorations {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.floating-icon {
  position: absolute;
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 24px;
  animation: float 6s ease-in-out infinite;
  transition: all 0.3s ease;
}

.floating-icon:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.icon-1 {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.icon-2 {
  top: 20%;
  right: 15%;
  animation-delay: 1s;
}

.icon-3 {
  top: 60%;
  left: 5%;
  animation-delay: 2s;
}

.icon-4 {
  top: 70%;
  right: 10%;
  animation-delay: 3s;
}

.icon-5 {
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

.icon-6 {
  bottom: 10%;
  right: 5%;
  animation-delay: 5s;
}

.hero-container {
  position: relative;
  z-index: 2;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-xl);
  align-items: center;
}

.hero-content {
  color: white;
  max-width: 500px;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  font-size: var(--font-sm);
  font-weight: 500;
  margin-bottom: var(--spacing-md);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.hero-title {
  font-size: var(--font-3xl);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
}

.title-line {
  display: block;
  margin-bottom: var(--spacing-xs);
}

.title-accent {
  display: block;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: var(--font-md);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.hero-btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--radius-lg);
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.hero-btn.primary {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  border: none;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
}

.hero-btn.primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

.hero-btn.secondary {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.hero-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.hero-visual {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.visual-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
  width: 100%;
  max-width: 400px;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
  animation: float 4s ease-in-out infinite;
}

.feature-card:nth-child(1) { animation-delay: 0s; }
.feature-card:nth-child(2) { animation-delay: 1s; }
.feature-card:nth-child(3) { animation-delay: 2s; }
.feature-card:nth-child(4) { animation-delay: 3s; }

.feature-card:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.2);
}

.card-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #333;
  margin-bottom: var(--spacing-sm);
}

.card-content h4 {
  color: white;
  font-size: var(--font-sm);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.card-content p {
  color: rgba(255, 255, 255, 0.8);
  font-size: var(--font-xs);
  line-height: 1.4;
}

/* 文章区域样式 */
.articles-section {
  padding: var(--spacing-xl) 0;
  background: var(--bg-color);
  position: relative;
}

.section-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  overflow: hidden;
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 20%, rgba(44, 62, 80, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(44, 62, 80, 0.03) 0%, transparent 50%);
  opacity: 0.3;
  animation: float 10s ease-in-out infinite;
}

.bg-decoration {
  position: absolute;
  width: 200px;
  height: 200px;
  background: linear-gradient(45deg, rgba(44, 62, 80, 0.05), rgba(52, 73, 94, 0.05));
  border-radius: 50%;
  filter: blur(10px);
  opacity: 0.3;
}

.bg-decoration.left {
  top: 10%;
  left: -10%;
  animation: float 15s ease-in-out infinite;
}

.bg-decoration.right {
  bottom: 10%;
  right: -10%;
  animation: float 15s ease-in-out infinite reverse;
}

.section-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* 三栏布局 */
.main-content-layout {
  display: grid;
  grid-template-columns: 280px 1fr 280px;
  gap: var(--spacing-xl);
  align-items: start;
}

.left-sidebar-wrapper,
.right-sidebar-wrapper {
  position: sticky;
  top: var(--spacing-lg);
}

.main-content-area {
  min-width: 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-lg);
  gap: var(--spacing-lg);
}

.header-content {
  flex: 1;
}

.section-title {
  font-size: var(--font-2xl);
  font-weight: 700;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.title-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.section-subtitle {
  font-size: var(--font-md);
  color: var(--text-secondary);
  line-height: 1.6;
}

.section-actions {
  display: flex;
  align-items: center;
}

.sort-buttons {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-xs);
  box-shadow: var(--shadow-sm);
}

.sort-buttons .el-button {
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: 500;
}

.sort-buttons .el-button.active {
  background: var(--primary-color);
  color: white;
}

.articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  text-align: center;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
}

.empty-icon {
  font-size: 48px;
  color: var(--text-light);
}

.empty-content h3 {
  font-size: var(--font-lg);
  color: var(--text-secondary);
  margin: 0;
}

.empty-content p {
  color: var(--text-light);
  margin: 0;
}

.load-more {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-lg);
}

.load-more-btn {
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--radius-lg);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.load-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* 动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .section-container {
    max-width: 1200px;
  }

  .main-content-layout {
    grid-template-columns: 260px 1fr 260px;
    gap: var(--spacing-lg);
  }
}

@media (max-width: 1200px) {
  .main-content-layout {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }

  .left-sidebar-wrapper,
  .right-sidebar-wrapper {
    display: none;
  }

  .main-content-area {
    max-width: 100%;
  }
}

@media (max-width: 1024px) {
  .hero-container {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--spacing-lg);
  }

  .hero-content {
    max-width: 100%;
  }

  .hero-visual {
    order: -1;
  }

  .visual-grid {
    grid-template-columns: repeat(2, 1fr);
    max-width: 500px;
  }

  .articles-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
  }

  /* 隐藏部分装饰性元素 */
  .floating-icon.icon-3,
  .floating-icon.icon-4 {
    display: none;
  }
}

@media (max-width: 768px) {
  .hero-section {
    min-height: 450px;
  }

  .hero-title {
    font-size: var(--font-2xl);
  }

  .hero-subtitle {
    font-size: var(--font-sm);
  }

  .hero-actions {
    justify-content: center;
  }

  .visual-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
  }

  .feature-card {
    padding: var(--spacing-sm);
  }

  .card-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .card-content h4 {
    font-size: var(--font-xs);
  }

  .card-content p {
    font-size: 10px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }

  .section-title {
    font-size: var(--font-2xl);
  }

  .title-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .articles-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  /* 隐藏更多装饰性元素 */
  .floating-icon.icon-2,
  .floating-icon.icon-5,
  .floating-icon.icon-6 {
    display: none;
  }

  .bg-decoration {
    display: none;
  }
}

@media (max-width: 480px) {
  .hero-section {
    min-height: 400px;
  }

  .hero-title {
    font-size: var(--font-xl);
  }

  .hero-actions {
    flex-direction: column;
    width: 100%;
  }

  .hero-btn {
    width: 100%;
    justify-content: center;
  }

  .visual-grid {
    grid-template-columns: 1fr;
    max-width: 300px;
  }

  .feature-card {
    flex-direction: row;
    text-align: left;
    padding: var(--spacing-sm);
  }

  .card-icon {
    margin-bottom: 0;
    margin-right: var(--spacing-sm);
  }

  .section-container {
    padding: 0 var(--spacing-md);
  }

  /* 隐藏所有装饰性元素 */
  .floating-icon {
    display: none;
  }
}
</style>
