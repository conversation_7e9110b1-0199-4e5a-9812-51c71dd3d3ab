package com.personal.blog.controller;

import com.personal.blog.dto.CommentCreateDTO;
import com.personal.blog.service.CommentService;
import com.personal.blog.vo.CommentVO;
import com.personal.blog.vo.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 评论控制器
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Tag(name = "评论管理", description = "评论相关接口")
@RestController
@RequestMapping("/api/comments")
@RequiredArgsConstructor
public class CommentController {

    private final CommentService commentService;

    @Operation(summary = "创建评论", description = "创建评论或回复。如果是回复，需要提供 parentId（第一层评论ID）和 replyToId（回复目标评论ID）")
    @PostMapping
    public Result<CommentVO> createComment(@Valid @RequestBody CommentCreateDTO commentCreateDTO) {
        CommentVO commentVO = commentService.createComment(commentCreateDTO);
        return Result.success("评论发表成功", commentVO);
    }

    @Operation(summary = "根据文章ID查询评论列表", description = "获取文章的两层评论结构：主评论 + 回复")
    @GetMapping
    public Result<List<CommentVO>> getCommentsByArticleId(
            @Parameter(description = "文章ID") @RequestParam Long articleId) {
        List<CommentVO> comments = commentService.getCommentsByArticleId(articleId);
        return Result.success(comments);
    }

    @Operation(summary = "删除评论")
    @DeleteMapping("/{id}")
    public Result<String> deleteComment(@Parameter(description = "评论ID") @PathVariable Long id) {
        commentService.deleteComment(id);
        return Result.success("评论删除成功");
    }

    @Operation(summary = "点赞评论")
    @PostMapping("/{id}/like")
    public Result<String> likeComment(@Parameter(description = "评论ID") @PathVariable Long id) {
        commentService.likeComment(id);
        return Result.success("点赞成功");
    }

    @Operation(summary = "取消点赞评论")
    @DeleteMapping("/{id}/like")
    public Result<String> unlikeComment(@Parameter(description = "评论ID") @PathVariable Long id) {
        commentService.unlikeComment(id);
        return Result.success("取消点赞成功");
    }
}
