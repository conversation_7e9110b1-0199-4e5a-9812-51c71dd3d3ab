<template>
  <div class="comment-item" :class="{ 'child-comment': depth > 0 }">
    <div class="comment-avatar">
      <img :src="getAvatarUrl(comment.userAvatar)" :alt="comment.userNickname">
    </div>
    <div class="comment-content">
      <div class="comment-header">
        <span class="comment-author">{{ comment.userNickname || '匿名用户' }}</span>
        <span class="comment-time">{{ formatDate(comment.createTime) }}</span>
      </div>
      <div class="comment-text">
        <span v-if="depth > 0 && comment.replyUserNickname" class="reply-chain">
          <span class="reply-to">@{{ comment.replyUserNickname }}</span>
          <span v-if="comment.replyChain" class="reply-chain-text">{{ comment.replyChain }}</span>
        </span>
        {{ comment.content }}
      </div>
      <div class="comment-actions">
        <div class="action-group">
          <el-button link size="small" @click="$emit('reply', comment)">
            <el-icon><ChatDotRound /></el-icon>
            回复
          </el-button>
          <el-button 
            v-if="userStore.isLoggedIn" 
            link 
            size="small" 
            type="danger"
            @click="$emit('delete', comment)"
          >
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
        </div>
        <div class="like-group">
          <el-button 
            link 
            size="small" 
            :type="comment.isLiked ? 'primary' : ''"
            @click="$emit('like', comment)"
          >
            <el-icon><Star /></el-icon>
            {{ comment.likeCount || 0 }}
          </el-button>
        </div>
      </div>
      
      <!-- 只显示第1层子评论 -->
      <div 
        v-if="comment.children && comment.children.length > 0 && depth === 0" 
        class="comment-children"
      >
        <div class="children-container">
          <CommentItem
            v-for="child in comment.children"
            :key="child.id"
            :comment="child"
            :depth="depth + 1"
            @reply="$emit('reply', $event)"
            @like="$emit('like', $event)"
            @delete="$emit('delete', $event)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ChatDotRound, Star, Delete } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/user'
import dayjs from 'dayjs'
import { getAvatarUrl } from '@/utils/avatar'

const props = defineProps({
  comment: {
    type: Object,
    required: true
  },
  depth: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['reply', 'like', 'delete'])

const userStore = useUserStore()

// 检查是否有删除权限
const canDelete = computed(() => {
  const hasPermission = userStore.isLoggedIn && (
    userStore.userInfo.id === props.comment.userId || 
    userStore.userInfo.role === 'ADMIN'
  )
  
  // 调试信息
  console.log('删除权限检查:', {
    isLoggedIn: userStore.isLoggedIn,
    userInfo: userStore.userInfo,
    commentUserId: props.comment.userId,
    hasPermission
  })
  
  return hasPermission
})

// 格式化日期
const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}
</script>

<style scoped>
.comment-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-white);
  border-radius: var(--radius-sm);
  border: none;
  margin-bottom: var(--spacing-sm);
}

.child-comment {
  margin-bottom: var(--spacing-sm);
  background: var(--bg-gray);
  border-left: 2px solid var(--primary-color);
  padding-left: var(--spacing-md);
}

.child-comment:last-child {
  margin-bottom: 0;
}

.comment-avatar img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.comment-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.comment-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.comment-author {
  font-weight: 600;
  color: var(--primary-color);
  font-size: var(--font-sm);
}

.comment-time {
  font-size: var(--font-xs);
  color: var(--text-secondary);
}

.comment-text {
  line-height: 1.5;
  color: var(--text-primary);
  font-size: var(--font-sm);
}

.comment-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-xs);
  gap: var(--spacing-md);
}

.action-group {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

.like-group {
  display: flex;
  align-items: center;
}

.comment-children {
  margin-top: var(--spacing-sm);
  margin-left: var(--spacing-md);
}

.children-container {
  transition: opacity 0.3s ease;
}

.reply-chain {
  display: inline;
}

.reply-to {
  color: var(--primary-color);
  font-weight: 500;
  margin-right: var(--spacing-xs);
}

.reply-chain-text {
  color: var(--text-secondary);
  font-size: var(--font-xs);
  margin-right: var(--spacing-xs);
}

/* 移动端样式 */
@media (max-width: 768px) {
  .comment-item {
    padding: var(--spacing-sm);
  }

  .comment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .comment-actions {
    gap: var(--spacing-sm);
  }
}
</style> 