<template>
  <div class="file-upload">
    <!-- 拖拽上传区域 -->
    <div
      class="upload-area"
      :class="{ 
        'is-dragover': isDragOver,
        'is-disabled': disabled,
        'has-file': hasFile
      }"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragleave="handleDragLeave"
      @click="triggerFileInput"
    >
      <!-- 上传图标 -->
      <div class="upload-icon">
        <el-icon v-if="!hasFile" size="48">
          <Upload />
        </el-icon>
        <el-icon v-else size="48" color="var(--success-color)">
          <Check />
        </el-icon>
      </div>

      <!-- 上传提示 -->
      <div class="upload-text">
        <template v-if="!hasFile">
          <p class="primary-text">{{ placeholder }}</p>
          <p class="secondary-text">{{ description }}</p>
        </template>
        <template v-else>
          <p class="primary-text">{{ fileName }}</p>
          <p class="secondary-text">{{ fileSize }}</p>
        </template>
      </div>

      <!-- 上传按钮 -->
      <el-button
        v-if="!hasFile"
        type="primary"
        size="small"
        :disabled="disabled"
        @click.stop="triggerFileInput"
      >
        选择文件
      </el-button>

      <!-- 文件操作按钮 -->
      <div v-else class="file-actions">
        <el-button
          type="success"
          size="small"
          @click.stop="handleUpload"
          :loading="uploading"
        >
          {{ uploading ? '上传中...' : '开始上传' }}
        </el-button>
        <el-button
          type="danger"
          size="small"
          @click.stop="removeFile"
        >
          移除
        </el-button>
      </div>
    </div>

    <!-- 隐藏的文件输入框 -->
    <input
      ref="fileInput"
      type="file"
      :accept="accept"
      :multiple="multiple"
      @change="handleFileChange"
      style="display: none"
    />

    <!-- 上传进度 -->
    <div v-if="uploading" class="upload-progress">
      <el-progress
        :percentage="uploadProgress"
        :status="uploadStatus"
        :stroke-width="8"
      />
    </div>

    <!-- 上传结果 -->
    <div v-if="uploadResult" class="upload-result">
      <el-alert
        :title="uploadResult.success ? '上传成功' : '上传失败'"
        :type="uploadResult.success ? 'success' : 'error'"
        :description="uploadResult.message"
        show-icon
        closable
        @close="clearUploadResult"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Upload, Check } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  // 文件类型限制
  accept: {
    type: String,
    default: '*'
  },
  // 是否允许多选
  multiple: {
    type: Boolean,
    default: false
  },
  // 文件大小限制（MB）
  maxSize: {
    type: Number,
    default: 10
  },
  // 占位符文本
  placeholder: {
    type: String,
    default: '点击或拖拽文件到此处上传'
  },
  // 描述文本
  description: {
    type: String,
    default: '支持 jpg、png、gif 格式，文件大小不超过 10MB'
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 自动上传
  autoUpload: {
    type: Boolean,
    default: false
  },
  // 上传函数
  uploadFunction: {
    type: Function,
    required: true
  }
})

const emit = defineEmits(['file-change', 'upload-success', 'upload-error', 'file-remove'])

// 响应式数据
const fileInput = ref(null)
const selectedFile = ref(null)
const isDragOver = ref(false)
const uploading = ref(false)
const uploadProgress = ref(0)
const uploadStatus = ref('')
const uploadResult = ref(null)

// 计算属性
const hasFile = computed(() => selectedFile.value !== null)
const fileName = computed(() => selectedFile.value?.name || '')
const fileSize = computed(() => {
  if (!selectedFile.value) return ''
  const size = selectedFile.value.size
  if (size < 1024) return size + ' B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + ' KB'
  return (size / (1024 * 1024)).toFixed(1) + ' MB'
})

// 触发文件选择
const triggerFileInput = () => {
  if (props.disabled) return
  fileInput.value?.click()
}

// 处理文件选择
const handleFileChange = (event) => {
  const files = event.target.files
  if (files.length === 0) return

  const file = files[0]
  validateAndSetFile(file)
}

// 处理拖拽
const handleDragOver = (event) => {
  event.preventDefault()
  if (!props.disabled) {
    isDragOver.value = true
  }
}

const handleDragLeave = (event) => {
  event.preventDefault()
  isDragOver.value = false
}

const handleDrop = (event) => {
  event.preventDefault()
  isDragOver.value = false
  
  if (props.disabled) return

  const files = event.dataTransfer.files
  if (files.length === 0) return

  const file = files[0]
  validateAndSetFile(file)
}

// 验证并设置文件
const validateAndSetFile = (file) => {
  // 验证文件类型
  if (props.accept !== '*' && !isValidFileType(file)) {
    ElMessage.error('文件类型不支持')
    return
  }

  // 验证文件大小
  if (file.size > props.maxSize * 1024 * 1024) {
    ElMessage.error(`文件大小不能超过 ${props.maxSize}MB`)
    return
  }

  selectedFile.value = file
  emit('file-change', file)

  // 自动上传
  if (props.autoUpload) {
    handleUpload()
  }
}

// 验证文件类型
const isValidFileType = (file) => {
  const acceptTypes = props.accept.split(',').map(type => type.trim())
  return acceptTypes.some(type => {
    if (type.startsWith('.')) {
      return file.name.toLowerCase().endsWith(type.toLowerCase())
    }
    if (type.includes('*')) {
      const baseType = type.split('/')[0]
      return file.type.startsWith(baseType)
    }
    return file.type === type
  })
}

// 移除文件
const removeFile = () => {
  selectedFile.value = null
  uploadResult.value = null
  uploadProgress.value = 0
  uploadStatus.value = ''
  emit('file-remove')
  
  // 清空文件输入框
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 处理上传
const handleUpload = async () => {
  if (!selectedFile.value || uploading.value) return

  try {
    uploading.value = true
    uploadProgress.value = 0
    uploadStatus.value = ''

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += Math.random() * 10
      }
    }, 100)

    // 调用上传函数
    const result = await props.uploadFunction(selectedFile.value)
    
    clearInterval(progressInterval)
    uploadProgress.value = 100
    uploadStatus.value = 'success'

    // 显示成功结果
    uploadResult.value = {
      success: true,
      message: '文件上传成功',
      data: result
    }

    emit('upload-success', result)
    ElMessage.success('文件上传成功')

  } catch (error) {
    uploadStatus.value = 'exception'
    uploadResult.value = {
      success: false,
      message: error.message || '文件上传失败'
    }

    emit('upload-error', error)
    ElMessage.error('文件上传失败')
  } finally {
    uploading.value = false
  }
}

// 清除上传结果
const clearUploadResult = () => {
  uploadResult.value = null
}

// 监听文件变化
watch(selectedFile, (newFile) => {
  if (newFile && props.autoUpload) {
    handleUpload()
  }
})

// 暴露方法
defineExpose({
  upload: handleUpload,
  remove: removeFile,
  clear: clearUploadResult
})
</script>

<style scoped>
.file-upload {
  width: 100%;
}

.upload-area {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  border: 2px dashed var(--border-light);
  border-radius: var(--radius-lg);
  background: var(--bg-gray);
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 200px;
}

.upload-area:hover {
  border-color: var(--primary-color);
  background: var(--bg-white);
}

.upload-area.is-dragover {
  border-color: var(--accent-color);
  background: rgba(var(--primary-color-rgb), 0.05);
  transform: scale(1.02);
}

.upload-area.is-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.upload-area.has-file {
  border-color: var(--success-color);
  background: rgba(var(--success-color-rgb), 0.05);
}

.upload-icon {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.upload-area:hover .upload-icon {
  color: var(--primary-color);
}

.upload-area.has-file .upload-icon {
  color: var(--success-color);
}

.upload-text {
  text-align: center;
  margin-bottom: var(--spacing-lg);
}

.primary-text {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.secondary-text {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
}

.file-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.upload-progress {
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-white);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.upload-result {
  margin-top: var(--spacing-md);
}

/* 移动端样式 */
@media (max-width: 768px) {
  .upload-area {
    padding: var(--spacing-lg);
    min-height: 150px;
  }

  .upload-icon {
    margin-bottom: var(--spacing-sm);
  }

  .upload-text {
    margin-bottom: var(--spacing-md);
  }

  .primary-text {
    font-size: var(--font-sm);
  }

  .secondary-text {
    font-size: var(--font-xs);
  }

  .file-actions {
    flex-direction: column;
    width: 100%;
  }
}
</style> 