<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.personal.blog.mapper.CommentMapper">

    <!-- 评论VO结果映射 -->
    <resultMap id="CommentVOMap" type="com.personal.blog.vo.CommentVO">
        <id column="id" property="id"/>
        <result column="article_id" property="articleId"/>
        <result column="user_id" property="userId"/>
        <result column="user_nickname" property="userNickname"/>
        <result column="user_avatar" property="userAvatar"/>
        <result column="parent_id" property="parentId"/>
        <result column="reply_to_id" property="replyToId"/>
        <result column="content" property="content"/>
        <result column="ip_address" property="ipAddress"/>
        <result column="like_count" property="likeCount"/>
        <result column="is_liked" property="isLiked"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="reply_user_id" property="replyUserId"/>
        <result column="reply_user_nickname" property="replyUserNickname"/>
        <result column="reply_user_avatar" property="replyUserAvatar"/>
        <result column="article_title" property="articleTitle"/>
    </resultMap>

    <!-- 根据文章ID查询评论列表 -->
    <select id="selectCommentsByArticleId" resultMap="CommentVOMap">
        SELECT 
            c.id,
            c.article_id,
            c.user_id,
            u.nickname as user_nickname,
            u.avatar as user_avatar,
            c.parent_id,
            c.reply_to_id,
            c.content,
            c.ip_address,
            c.like_count,
            CASE WHEN cl.id IS NOT NULL THEN 1 ELSE 0 END as is_liked,
            c.status,
            c.create_time,
            r.user_id as reply_user_id,
            ru.nickname as reply_user_nickname,
            ru.avatar as reply_user_avatar
        FROM tb_comment c
        LEFT JOIN tb_user u ON c.user_id = u.id
        LEFT JOIN tb_comment r ON c.reply_to_id = r.id
        LEFT JOIN tb_user ru ON r.user_id = ru.id
        LEFT JOIN tb_comment_like cl ON c.id = cl.comment_id AND cl.user_id = #{currentUserId}
        WHERE c.article_id = #{articleId} 
          AND c.status = 1
        ORDER BY c.parent_id ASC, c.create_time ASC
    </select>

    <!-- 查询用户评论列表 -->
    <select id="selectUserComments" resultMap="CommentVOMap">
        SELECT 
            c.id,
            c.article_id,
            c.user_id,
            u.nickname as user_nickname,
            u.avatar as user_avatar,
            c.parent_id,
            c.reply_to_id,
            c.content,
            c.ip_address,
            c.status,
            c.create_time,
            r.user_id as reply_user_id,
            ru.nickname as reply_user_nickname,
            ru.avatar as reply_user_avatar,
            a.title as article_title
        FROM tb_comment c
        LEFT JOIN tb_user u ON c.user_id = u.id
        LEFT JOIN tb_comment r ON c.reply_to_id = r.id
        LEFT JOIN tb_user ru ON r.user_id = ru.id
        LEFT JOIN tb_article a ON c.article_id = a.id
        WHERE c.user_id = #{userId} 
          AND c.status = 1
        ORDER BY c.create_time DESC
    </select>

</mapper>
