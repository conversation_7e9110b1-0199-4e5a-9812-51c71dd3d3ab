<template>
  <div class="file-upload-demo">
    <div class="demo-header">
      <h1>文件上传演示</h1>
      <p>这是一个完整的文件上传系统演示，包括头像上传、文章图片上传和通用文件上传</p>
    </div>

    <div class="demo-content">
      <!-- 头像上传 -->
      <div class="demo-section">
        <h2>头像上传</h2>
        <p class="section-desc">支持拖拽上传，自动验证文件类型和大小</p>
        <div class="upload-container">
          <FileUpload
            ref="avatarUploadRef"
            accept=".jpg,.jpeg,.png,.gif,.webp"
            :max-size="5"
            placeholder="点击或拖拽头像到此处"
            description="支持 jpg、png、gif、webp 格式，文件大小不超过 5MB"
            :auto-upload="true"
            :upload-function="handleAvatarUpload"
            @upload-success="handleAvatarUploadSuccess"
            @upload-error="handleAvatarUploadError"
          />
        </div>
        <div v-if="avatarResult" class="upload-result">
          <h3>上传结果：</h3>
          <pre>{{ JSON.stringify(avatarResult, null, 2) }}</pre>
        </div>
      </div>

      <!-- 文章图片上传 -->
      <div class="demo-section">
        <h2>文章图片上传</h2>
        <p class="section-desc">用于文章内容中的图片上传</p>
        <div class="upload-container">
          <FileUpload
            ref="articleImageUploadRef"
            accept=".jpg,.jpeg,.png,.gif,.webp"
            :max-size="10"
            placeholder="点击或拖拽图片到此处"
            description="支持 jpg、png、gif、webp 格式，文件大小不超过 10MB"
            :auto-upload="false"
            :upload-function="handleArticleImageUpload"
            @upload-success="handleArticleImageUploadSuccess"
            @upload-error="handleArticleImageUploadError"
          />
        </div>
        <div v-if="articleImageResult" class="upload-result">
          <h3>上传结果：</h3>
          <pre>{{ JSON.stringify(articleImageResult, null, 2) }}</pre>
        </div>
      </div>

      <!-- 通用文件上传 -->
      <div class="demo-section">
        <h2>通用文件上传</h2>
        <p class="section-desc">支持各种类型的文件上传</p>
        <div class="upload-container">
          <FileUpload
            ref="fileUploadRef"
            accept="*"
            :max-size="50"
            placeholder="点击或拖拽文件到此处"
            description="支持各种文件格式，文件大小不超过 50MB"
            :auto-upload="false"
            :upload-function="handleFileUpload"
            @upload-success="handleFileUploadSuccess"
            @upload-error="handleFileUploadError"
          />
        </div>
        <div v-if="fileResult" class="upload-result">
          <h3>上传结果：</h3>
          <pre>{{ JSON.stringify(fileResult, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import FileUpload from '@/components/common/FileUpload.vue'
import { uploadAvatar, uploadArticleImage, uploadFile } from '@/api/file'

// 响应式数据
const avatarUploadRef = ref()
const articleImageUploadRef = ref()
const fileUploadRef = ref()

const avatarResult = ref(null)
const articleImageResult = ref(null)
const fileResult = ref(null)

// 头像上传处理
const handleAvatarUpload = async (file) => {
  try {
    const response = await uploadAvatar(file)
    return response.data
  } catch (error) {
    throw new Error('头像上传失败')
  }
}

const handleAvatarUploadSuccess = (result) => {
  avatarResult.value = result
  ElMessage.success('头像上传成功')
}

const handleAvatarUploadError = (error) => {
  ElMessage.error('头像上传失败')
}

// 文章图片上传处理
const handleArticleImageUpload = async (file) => {
  try {
    const response = await uploadArticleImage(file)
    return response.data
  } catch (error) {
    throw new Error('图片上传失败')
  }
}

const handleArticleImageUploadSuccess = (result) => {
  articleImageResult.value = result
  ElMessage.success('图片上传成功')
}

const handleArticleImageUploadError = (error) => {
  ElMessage.error('图片上传失败')
}

// 通用文件上传处理
const handleFileUpload = async (file) => {
  try {
    const response = await uploadFile(file)
    return response.data
  } catch (error) {
    throw new Error('文件上传失败')
  }
}

const handleFileUploadSuccess = (result) => {
  fileResult.value = result
  ElMessage.success('文件上传成功')
}

const handleFileUploadError = (error) => {
  ElMessage.error('文件上传失败')
}
</script>

<style scoped>
.file-upload-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

.demo-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-xl);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
  border-radius: var(--radius-lg);
  color: white;
}

.demo-header h1 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--font-3xl);
  font-weight: 700;
}

.demo-header p {
  margin: 0;
  font-size: var(--font-lg);
  opacity: 0.9;
}

.demo-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.demo-section {
  background: var(--bg-white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
}

.demo-section h2 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--primary-color);
  font-size: var(--font-xl);
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.demo-section h2::before {
  content: '';
  width: 4px;
  height: 24px;
  background: linear-gradient(180deg, var(--primary-color), var(--accent-color));
  border-radius: 2px;
}

.section-desc {
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-lg) 0;
  font-size: var(--font-md);
}

.upload-container {
  margin-bottom: var(--spacing-lg);
}

.upload-result {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--bg-gray);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.upload-result h3 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--primary-color);
  font-size: var(--font-lg);
}

.upload-result pre {
  background: var(--bg-white);
  padding: var(--spacing-md);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-light);
  overflow-x: auto;
  font-size: var(--font-sm);
  line-height: 1.5;
  color: var(--text-primary);
}

/* 移动端样式 */
@media (max-width: 768px) {
  .file-upload-demo {
    padding: var(--spacing-md);
  }

  .demo-header {
    padding: var(--spacing-lg);
  }

  .demo-header h1 {
    font-size: var(--font-2xl);
  }

  .demo-header p {
    font-size: var(--font-md);
  }

  .demo-section {
    padding: var(--spacing-lg);
  }

  .demo-section h2 {
    font-size: var(--font-lg);
  }

  .section-desc {
    font-size: var(--font-sm);
  }

  .upload-result {
    padding: var(--spacing-md);
  }

  .upload-result pre {
    font-size: var(--font-xs);
    padding: var(--spacing-sm);
  }
}
</style> 