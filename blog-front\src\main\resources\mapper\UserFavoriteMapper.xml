<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.personal.blog.mapper.UserFavoriteMapper">

    <!-- 文章VO结果映射 -->
    <resultMap id="ArticleVOMap" type="com.personal.blog.vo.ArticleVO">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="summary" property="summary"/>
        <result column="content" property="content"/>
        <result column="cover_image" property="coverImage"/>
        <result column="category_id" property="categoryId"/>
        <result column="category_name" property="categoryName"/>
        <result column="author_id" property="authorId"/>
        <result column="author_nickname" property="authorNickname"/>
        <result column="view_count" property="viewCount"/>
        <result column="like_count" property="likeCount"/>
        <result column="comment_count" property="commentCount"/>
        <result column="is_published" property="isPublished"/>
        <result column="is_top" property="isTop"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 查询用户收藏的文章列表 -->
    <select id="selectUserFavoriteArticles" resultMap="ArticleVOMap">
        SELECT 
            a.id,
            a.title,
            a.summary,
            a.content,
            a.cover_image,
            a.category_id,
            c.name as category_name,
            a.author_id,
            u.nickname as author_nickname,
            a.view_count,
            a.like_count,
            a.comment_count,
            a.is_published,
            a.is_top,
            a.create_time,
            a.update_time
        FROM tb_article a
        LEFT JOIN tb_category c ON a.category_id = c.id
        LEFT JOIN tb_user u ON a.author_id = u.id
        INNER JOIN tb_user_favorite uf ON a.id = uf.article_id
        WHERE uf.user_id = #{userId} 
          AND uf.status = 1
        ORDER BY uf.create_time DESC
    </select>

</mapper> 