import { request } from '@/utils/request'

/**
 * 评论相关 API
 * 
 * 评论结构说明：
 * - 主评论：parentId = 0, replyToId = 0
 * - 回复主评论：parentId = 主评论ID, replyToId = 主评论ID  
 * - 回复其他回复：parentId = 主评论ID, replyToId = 被回复的评论ID
 */

// 获取评论列表
export const getComments = (params) => {
  return request.get('/comments', params)
}

// 获取文章评论
export const getArticleComments = (articleId, params) => {
  return request.get(`/comments?articleId=${articleId}`, params)
}

// 发表评论
export const createComment = (data) => {
  return request.post('/comments', data)
}

// 回复评论（已废弃，现在统一使用 createComment）
export const replyComment = (commentId, data) => {
  return request.post(`/comments/${commentId}/reply`, data)
}

// 删除评论
export const deleteComment = (id) => {
  return request.delete(`/comments/${id}`)
}

// 点赞评论
export const likeComment = (id) => {
  return request.post(`/comments/${id}/like`)
}

// 取消点赞评论
export const unlikeComment = (id) => {
  return request.delete(`/comments/${id}/like`)
}
