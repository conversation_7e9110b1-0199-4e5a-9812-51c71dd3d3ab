package com.personal.blog.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.personal.blog.entity.Article;
import com.personal.blog.entity.UserLike;
import com.personal.blog.mapper.ArticleMapper;
import com.personal.blog.mapper.UserLikeMapper;
import com.personal.blog.service.UserLikeService;
import com.personal.blog.vo.ArticleVO;
import com.personal.blog.vo.PageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户点赞Service实现类
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Service
@RequiredArgsConstructor
public class UserLikeServiceImpl extends ServiceImpl<UserLikeMapper, UserLike> implements UserLikeService {

    private final ArticleMapper articleMapper;

    @Override
    @Transactional
    public boolean likeArticle(Long articleId) {
        Long currentUserId = StpUtil.getLoginIdAsLong();
        
        // 检查文章是否存在
        Article article = articleMapper.selectById(articleId);
        if (article == null) {
            return false;
        }
        
        // 检查是否已点赞
        UserLike existLike = this.getOne(new LambdaQueryWrapper<UserLike>()
                .eq(UserLike::getUserId, currentUserId)
                .eq(UserLike::getArticleId, articleId));
        
        if (existLike != null) {
            if (existLike.getStatus() == 1) {
                return true; // 已经点赞了
            } else {
                // 更新为已点赞
                existLike.setStatus(1);
                this.updateById(existLike);
            }
        } else {
            // 创建新的点赞记录
            UserLike userLike = new UserLike();
            userLike.setUserId(currentUserId);
            userLike.setArticleId(articleId);
            userLike.setStatus(1);
            this.save(userLike);
        }
        
        // 更新文章点赞数
        articleMapper.update(null, new LambdaUpdateWrapper<Article>()
                .eq(Article::getId, articleId)
                .setSql("like_count = like_count + 1"));
        
        return true;
    }

    @Override
    @Transactional
    public boolean unlikeArticle(Long articleId) {
        Long currentUserId = StpUtil.getLoginIdAsLong();
        
        // 检查是否已点赞
        UserLike existLike = this.getOne(new LambdaQueryWrapper<UserLike>()
                .eq(UserLike::getUserId, currentUserId)
                .eq(UserLike::getArticleId, articleId));
        
        if (existLike == null || existLike.getStatus() == 0) {
            return true; // 本来就没有点赞
        }
        
        // 更新为取消点赞
        existLike.setStatus(0);
        this.updateById(existLike);
        
        // 更新文章点赞数
        articleMapper.update(null, new LambdaUpdateWrapper<Article>()
                .eq(Article::getId, articleId)
                .setSql("like_count = like_count - 1"));
        
        return true;
    }

    @Override
    public boolean isLiked(Long articleId) {
        Long currentUserId = StpUtil.getLoginIdAsLong();
        
        UserLike userLike = this.getOne(new LambdaQueryWrapper<UserLike>()
                .eq(UserLike::getUserId, currentUserId)
                .eq(UserLike::getArticleId, articleId));
        
        return userLike != null && userLike.getStatus() == 1;
    }

    @Override
    public PageResult<ArticleVO> getUserLikedArticles(Integer current, Integer size) {
        Long currentUserId = StpUtil.getLoginIdAsLong();
        
        Page<ArticleVO> page = new Page<>(current, size);
        IPage<ArticleVO> result = baseMapper.selectUserLikedArticles(page, currentUserId);
        
        return new PageResult<>(result.getRecords(), result.getTotal(), 
                result.getCurrent(), result.getSize());
    }

    @Override
    public void removeLike(Long articleId) {
        Long currentUserId = StpUtil.getLoginIdAsLong();
        
        // 检查是否已点赞
        UserLike existLike = this.getOne(new LambdaQueryWrapper<UserLike>()
                .eq(UserLike::getUserId, currentUserId)
                .eq(UserLike::getArticleId, articleId));
        
        if (existLike == null || existLike.getStatus() == 0) {
            return; // 本来就没有点赞
        }
        
        // 更新为取消点赞
        existLike.setStatus(0);
        this.updateById(existLike);
        
        // 更新文章点赞数
        articleMapper.update(null, new LambdaUpdateWrapper<Article>()
                .eq(Article::getId, articleId)
                .setSql("like_count = like_count - 1"));
    }
} 