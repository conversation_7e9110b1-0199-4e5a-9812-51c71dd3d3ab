package com.personal.blog.controller;

import com.personal.blog.dto.UserUpdateDTO;
import com.personal.blog.service.ArticleService;
import com.personal.blog.service.CommentService;
import com.personal.blog.service.UserFavoriteService;
import com.personal.blog.service.UserLikeService;
import com.personal.blog.service.UserService;
import com.personal.blog.vo.ArticleVO;
import com.personal.blog.vo.CommentVO;
import com.personal.blog.vo.PageResult;
import com.personal.blog.vo.Result;
import com.personal.blog.vo.UserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 用户控制器
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Tag(name = "用户管理", description = "用户信息相关接口")
@RestController
@RequestMapping("/api/user")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;
    private final ArticleService articleService;
    private final CommentService commentService;
    private final UserLikeService userLikeService;
    private final UserFavoriteService userFavoriteService;

    @Operation(summary = "获取当前用户信息")
    @GetMapping("/profile")
    public Result<UserVO> getCurrentUser() {
        UserVO userVO = userService.getCurrentUser();
        return Result.success(userVO);
    }

    @Operation(summary = "更新用户信息")
    @PutMapping("/profile")
    public Result<UserVO> updateUserProfile(@Valid @RequestBody UserUpdateDTO userUpdateDTO) {
        UserVO userVO = userService.updateUserProfile(userUpdateDTO);
        return Result.success("用户信息更新成功", userVO);
    }

    @Operation(summary = "获取用户文章列表")
    @GetMapping("/articles")
    public Result<PageResult<ArticleVO>> getUserArticles(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        PageResult<ArticleVO> pageResult = articleService.getUserArticles(current, size);
        return Result.success(pageResult);
    }

    @Operation(summary = "获取用户点赞的文章")
    @GetMapping("/likes")
    public Result<PageResult<ArticleVO>> getUserLikedArticles(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        PageResult<ArticleVO> pageResult = userLikeService.getUserLikedArticles(current, size);
        return Result.success(pageResult);
    }

    @Operation(summary = "获取用户收藏的文章")
    @GetMapping("/favorites")
    public Result<PageResult<ArticleVO>> getUserFavoriteArticles(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        PageResult<ArticleVO> pageResult = userFavoriteService.getUserFavoriteArticles(current, size);
        return Result.success(pageResult);
    }

    @Operation(summary = "获取用户评论")
    @GetMapping("/comments")
    public Result<PageResult<CommentVO>> getUserComments(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size) {
        PageResult<CommentVO> pageResult = commentService.getUserComments(current, size);
        return Result.success(pageResult);
    }

    @Operation(summary = "删除用户评论")
    @DeleteMapping("/comments/{id}")
    public Result<String> deleteUserComment(@Parameter(description = "评论ID") @PathVariable Long id) {
        commentService.deleteUserComment(id);
        return Result.success("评论删除成功");
    }

    @Operation(summary = "取消收藏文章")
    @DeleteMapping("/favorites/{articleId}")
    public Result<String> removeFavorite(@Parameter(description = "文章ID") @PathVariable Long articleId) {
        userFavoriteService.removeFavorite(articleId);
        return Result.success("取消收藏成功");
    }

    @Operation(summary = "取消点赞文章")
    @DeleteMapping("/likes/{articleId}")
    public Result<String> removeLike(@Parameter(description = "文章ID") @PathVariable Long articleId) {
        userLikeService.removeLike(articleId);
        return Result.success("取消点赞成功");
    }
}
