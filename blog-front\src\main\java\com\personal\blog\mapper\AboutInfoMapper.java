package com.personal.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.personal.blog.entity.AboutInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

/**
 * 关于页面信息 Mapper 接口
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Mapper
public interface AboutInfoMapper extends BaseMapper<AboutInfo> {
    
    /**
     * 使用原生SQL查询关于页面信息，并映射到AboutInfo实体
     */
    @Select("SELECT id, user_id as userId, title, description, contact_email as contactEmail, " +
            "contact_github as contactGithub, contact_location as contactLocation, " +
            "social_links as socialLinks, skills_frontend as skillsFrontend, " +
            "skills_backend as skillsBackend, skills_database as skillsDatabase, " +
            "skills_tools as skillsTools, create_time as createTime, update_time as updateTime " +
            "FROM tb_about_info LIMIT 1")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "userId", column = "userId"),
        @Result(property = "title", column = "title"),
        @Result(property = "description", column = "description", typeHandler = com.personal.blog.config.JsonTypeHandler.class),
        @Result(property = "contactEmail", column = "contactEmail"),
        @Result(property = "contactGithub", column = "contactGithub"),
        @Result(property = "contactLocation", column = "contactLocation"),
        @Result(property = "socialLinks", column = "socialLinks", typeHandler = com.personal.blog.config.JsonTypeHandler.class),
        @Result(property = "skillsFrontend", column = "skillsFrontend", typeHandler = com.personal.blog.config.JsonTypeHandler.class),
        @Result(property = "skillsBackend", column = "skillsBackend", typeHandler = com.personal.blog.config.JsonTypeHandler.class),
        @Result(property = "skillsDatabase", column = "skillsDatabase", typeHandler = com.personal.blog.config.JsonTypeHandler.class),
        @Result(property = "skillsTools", column = "skillsTools", typeHandler = com.personal.blog.config.JsonTypeHandler.class),
        @Result(property = "createTime", column = "createTime"),
        @Result(property = "updateTime", column = "updateTime")
    })
    AboutInfo selectAboutInfoWithMapping();
} 