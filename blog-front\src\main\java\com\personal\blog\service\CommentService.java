package com.personal.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.personal.blog.dto.CommentCreateDTO;
import com.personal.blog.entity.Comment;
import com.personal.blog.vo.CommentVO;
import com.personal.blog.vo.PageResult;

import java.util.List;

/**
 * 评论Service接口
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
public interface CommentService extends IService<Comment> {

    /**
     * 创建评论
     * 
     * @param commentCreateDTO 评论创建信息
     * @return 评论信息
     */
    CommentVO createComment(CommentCreateDTO commentCreateDTO);

    /**
     * 根据文章ID查询评论列表
     * 
     * @param articleId 文章ID
     * @return 评论列表
     */
    List<CommentVO> getCommentsByArticleId(Long articleId);

    /**
     * 删除评论
     * 
     * @param id 评论ID
     */
    void deleteComment(Long id);

    /**
     * 获取当前用户的评论列表
     * 
     * @param current 当前页
     * @param size 每页大小
     * @return 评论列表
     */
    PageResult<CommentVO> getUserComments(Integer current, Integer size);

    /**
     * 删除用户评论
     * 
     * @param id 评论ID
     */
    void deleteUserComment(Long id);

    /**
     * 点赞评论
     * 
     * @param id 评论ID
     */
    void likeComment(Long id);

    /**
     * 取消点赞评论
     * 
     * @param id 评论ID
     */
    void unlikeComment(Long id);
}
