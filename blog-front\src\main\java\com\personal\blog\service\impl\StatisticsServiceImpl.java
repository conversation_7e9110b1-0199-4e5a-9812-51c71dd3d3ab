package com.personal.blog.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.personal.blog.entity.Article;
import com.personal.blog.entity.Comment;
import com.personal.blog.mapper.ArticleMapper;
import com.personal.blog.mapper.CommentMapper;
import com.personal.blog.service.StatisticsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 统计服务实现类
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Service
@RequiredArgsConstructor
public class StatisticsServiceImpl implements StatisticsService {

    private final ArticleMapper articleMapper;
    private final CommentMapper commentMapper;

    @Override
    public Map<String, Object> getStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 统计文章总数
        LambdaQueryWrapper<Article> articleWrapper = new LambdaQueryWrapper<>();
        articleWrapper.eq(Article::getIsPublished, 1);
        long articleCount = articleMapper.selectCount(articleWrapper);
        
        // 统计总访问量
        Integer totalViewCount = articleMapper.selectTotalViewCount();
        
        // 统计评论总数
        LambdaQueryWrapper<Comment> commentWrapper = new LambdaQueryWrapper<>();
        commentWrapper.eq(Comment::getStatus, 1);
        long commentCount = commentMapper.selectCount(commentWrapper);
        
        statistics.put("articleCount", articleCount);
        statistics.put("viewCount", totalViewCount != null ? totalViewCount : 0);
        statistics.put("commentCount", commentCount);
        
        return statistics;
    }
    
    @Override
    public Map<String, Object> getAuthorStatistics(Long authorId) {
        Map<String, Object> statistics = new HashMap<>();
        
        // 统计该作者的文章总数
        LambdaQueryWrapper<Article> articleWrapper = new LambdaQueryWrapper<>();
        articleWrapper.eq(Article::getIsPublished, 1)
                    .eq(Article::getAuthorId, authorId);
        long articleCount = articleMapper.selectCount(articleWrapper);
        
        // 统计该作者文章的总访问量
        Integer totalViewCount = articleMapper.selectAuthorTotalViewCount(authorId);
        
        // 统计该作者文章的评论总数
        LambdaQueryWrapper<Comment> commentWrapper = new LambdaQueryWrapper<>();
        commentWrapper.eq(Comment::getStatus, 1)
                     .inSql(Comment::getArticleId, 
                           "SELECT id FROM tb_article WHERE author_id = " + authorId + " AND is_published = 1");
        long commentCount = commentMapper.selectCount(commentWrapper);
        
        statistics.put("articleCount", articleCount);
        statistics.put("viewCount", totalViewCount != null ? totalViewCount : 0);
        statistics.put("commentCount", commentCount);
        
        return statistics;
    }
} 