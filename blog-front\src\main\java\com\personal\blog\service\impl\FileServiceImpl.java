package com.personal.blog.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.personal.blog.exception.BusinessException;
import com.personal.blog.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件上传Service实现类
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Slf4j
@Service
public class FileServiceImpl implements FileService {

    @Value("${app.upload.path:uploads}")
    private String uploadPath;

    @Value("${app.upload.max-size:10485760}")
    private long maxFileSize; // 默认10MB

    @Value("${app.upload.allowed-types:jpg,jpeg,png,gif,webp}")
    private String allowedTypes;

    @Override
    public Map<String, String> uploadAvatar(MultipartFile file) {
        // 验证文件
        validateFile(file, "头像");
        
        // 验证图片类型
        validateImageType(file);
        
        // 生成文件名
        String originalFilename = file.getOriginalFilename();
        String extension = getFileExtension(originalFilename);
        String filename = "avatar_" + IdUtil.fastSimpleUUID() + "." + extension;
        
        // 保存文件
        String relativePath = "avatars/" + filename;
        String fullPath = saveFile(file, relativePath);
        
        Map<String, String> result = new HashMap<>();
        result.put("url", "/api/files/access?path=" + relativePath);
        result.put("filename", originalFilename);
        result.put("size", String.valueOf(file.getSize()));
        
        return result;
    }

    @Override
    public Map<String, String> uploadArticleImage(MultipartFile file) {
        // 验证文件
        validateFile(file, "图片");
        
        // 验证图片类型
        validateImageType(file);
        
        // 生成文件名
        String originalFilename = file.getOriginalFilename();
        String extension = getFileExtension(originalFilename);
        String filename = "article_" + IdUtil.fastSimpleUUID() + "." + extension;
        
        // 保存文件
        String relativePath = "articles/" + filename;
        String fullPath = saveFile(file, relativePath);
        
        Map<String, String> result = new HashMap<>();
        result.put("url", "/api/files/access?path=" + relativePath);
        result.put("filename", originalFilename);
        result.put("size", String.valueOf(file.getSize()));
        
        return result;
    }

    @Override
    public Map<String, String> uploadFile(MultipartFile file) {
        // 验证文件
        validateFile(file, "文件");
        
        // 生成文件名
        String originalFilename = file.getOriginalFilename();
        String extension = getFileExtension(originalFilename);
        String filename = "file_" + IdUtil.fastSimpleUUID() + "." + extension;
        
        // 保存文件
        String relativePath = "files/" + filename;
        String fullPath = saveFile(file, relativePath);
        
        Map<String, String> result = new HashMap<>();
        result.put("url", "/api/files/access?path=" + relativePath);
        result.put("filename", originalFilename);
        result.put("size", String.valueOf(file.getSize()));
        
        return result;
    }

    @Override
    public boolean deleteFile(String fileUrl) {
        if (StrUtil.isBlank(fileUrl)) {
            return false;
        }
        
        try {
            // 处理API访问URL格式
            String relativePath = fileUrl;
            if (fileUrl.contains("path=")) {
                relativePath = fileUrl.substring(fileUrl.indexOf("path=") + 5);
            } else if (fileUrl.startsWith("/")) {
                relativePath = fileUrl.substring(1);
            }
            Path filePath = Paths.get(uploadPath, relativePath);
            
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                log.info("文件删除成功: {}", filePath);
                return true;
            }
        } catch (IOException e) {
            log.error("文件删除失败: {}", fileUrl, e);
        }
        
        return false;
    }

    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file, String fileType) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException(fileType + "不能为空");
        }
        
        if (file.getSize() > maxFileSize) {
            throw new BusinessException(fileType + "大小不能超过" + (maxFileSize / 1024 / 1024) + "MB");
        }
    }

    /**
     * 验证图片类型
     */
    private void validateImageType(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        String extension = getFileExtension(originalFilename);
        
        if (StrUtil.isBlank(extension)) {
            throw new BusinessException("文件格式不支持");
        }
        
        String[] allowedExtensions = allowedTypes.split(",");
        boolean isValid = false;
        
        for (String allowedExt : allowedExtensions) {
            if (allowedExt.trim().equalsIgnoreCase(extension)) {
                isValid = true;
                break;
            }
        }
        
        if (!isValid) {
            throw new BusinessException("只支持以下格式: " + allowedTypes);
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (StrUtil.isBlank(filename)) {
            return "";
        }
        
        int lastDotIndex = filename.lastIndexOf(".");
        if (lastDotIndex == -1) {
            return "";
        }
        
        return filename.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * 保存文件
     */
    private String saveFile(MultipartFile file, String relativePath) {
        try {
            // 获取项目根目录
            String projectRoot = System.getProperty("user.dir");
            Path uploadDir = Paths.get(projectRoot, uploadPath);
            
            // 创建上传目录
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }
            
            // 创建子目录
            Path filePath = Paths.get(projectRoot, uploadPath, relativePath);
            Path parentDir = filePath.getParent();
            if (!Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
            }
            
            // 保存文件
            file.transferTo(filePath.toFile());
            
            log.info("文件上传成功: {}", filePath);
            return filePath.toString();
            
        } catch (IOException e) {
            log.error("文件上传失败", e);
            throw new BusinessException("文件上传失败: " + e.getMessage());
        }
    }
} 