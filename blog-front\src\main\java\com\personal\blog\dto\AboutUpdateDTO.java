package com.personal.blog.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * 关于页面更新DTO
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Data
@Schema(description = "关于页面更新DTO")
public class AboutUpdateDTO {

    @Schema(description = "标题")
    @Size(max = 200, message = "标题长度不能超过200个字符")
    private String title;

    @Schema(description = "个人描述")
    private List<String> description;

    @Schema(description = "联系方式")
    private ContactInfo contact;

    @Schema(description = "社交链接")
    private List<SocialLink> social;

    @Data
    @Schema(description = "联系方式")
    public static class ContactInfo {
        @Schema(description = "邮箱")
        @Email(message = "邮箱格式不正确")
        private String email;

        @Schema(description = "GitHub")
        private String github;

        @Schema(description = "位置")
        private String location;
    }

    @Data
    @Schema(description = "社交链接")
    public static class SocialLink {
        @Schema(description = "名称")
        @Size(max = 50, message = "名称长度不能超过50个字符")
        private String name;

        @Schema(description = "图标")
        private String icon;

        @Schema(description = "链接")
        private String url;
    }
} 