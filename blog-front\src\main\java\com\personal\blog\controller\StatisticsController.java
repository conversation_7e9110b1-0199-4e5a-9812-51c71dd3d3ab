package com.personal.blog.controller;

import com.personal.blog.service.StatisticsService;
import com.personal.blog.vo.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 统计控制器
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Tag(name = "统计", description = "网站统计数据相关接口")
@RestController
@RequestMapping("/api/statistics")
@RequiredArgsConstructor
public class StatisticsController {

    private final StatisticsService statisticsService;

    @Operation(summary = "获取网站统计数据")
    @GetMapping("/overview")
    public Result<Map<String, Object>> getStatistics() {
        Map<String, Object> statistics = statisticsService.getStatistics();
        return Result.success("获取统计数据成功", statistics);
    }
    
    @Operation(summary = "获取作者统计数据")
    @GetMapping("/author/{authorId}")
    public Result<Map<String, Object>> getAuthorStatistics(@PathVariable Long authorId) {
        Map<String, Object> statistics = statisticsService.getAuthorStatistics(authorId);
        return Result.success("获取作者统计数据成功", statistics);
    }
} 