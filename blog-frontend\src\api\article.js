import { request } from '@/utils/request'

// 获取文章列表
export const getArticles = (params) => {
  return request.get('/articles', params)
}

// 获取文章详情
export const getArticleById = (id) => {
  return request.get(`/articles/${id}`)
}

// 创建文章
export const createArticle = (data) => {
  return request.post('/articles', data)
}

// 更新文章
export const updateArticle = (id, data) => {
  return request.put(`/articles/${id}`, data)
}

// 删除文章
export const deleteArticle = (id) => {
  return request.delete(`/articles/${id}`)
}

// 点赞文章
export const likeArticle = (id) => {
  return request.post(`/articles/${id}/like`)
}

// 取消点赞
export const unlikeArticle = (id) => {
  return request.delete(`/articles/${id}/like`)
}

// 收藏文章
export const favoriteArticle = (id) => {
  return request.post(`/articles/${id}/favorite`)
}

// 取消收藏
export const unfavoriteArticle = (id) => {
  return request.delete(`/articles/${id}/favorite`)
}

// 检查是否已点赞
export const checkLiked = (id) => {
  return request.get(`/articles/${id}/like`)
}

// 检查是否已收藏
export const checkFavorited = (id) => {
  return request.get(`/articles/${id}/favorite`)
}

// 获取分类列表
export const getCategories = () => {
  return request.get('/categories')
}

// 搜索文章
export const searchArticles = (params) => {
  return request.get('/articles/search', params)
}

// 获取热门文章
export const getHotArticles = (params) => {
  return request.get('/articles/hot', params)
}

// 获取作者文章列表
export const getAuthorArticles = (authorId) => {
  return request.get(`/articles/author/${authorId}`)
}
