server:
  port: 8080
  # 监听所有网络接口，这样可以获取到真实的客户端IP
  address: 0.0.0.0

spring:
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************************************************************************************************************************************************************************************************
    username: root
    password: root
    
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0

# 日志配置
logging:
  level:
    com.personal.blog: debug
    com.personal.blog.mapper: debug
    org.springframework: info
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n'

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 启用自动映射
    auto-mapping-behavior: full
    # 启用驼峰命名转换
    map-underscore-to-camel-case: true
