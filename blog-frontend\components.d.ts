/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AdminLayout: typeof import('./src/components/layout/AdminLayout.vue')['default']
    ArticleCard: typeof import('./src/components/article/ArticleCard.vue')['default']
    AuthLayout: typeof import('./src/components/layout/AuthLayout.vue')['default']
    BackToTop: typeof import('./src/components/common/BackToTop.vue')['default']
    BlogFooter: typeof import('./src/components/layout/BlogFooter.vue')['default']
    BlogHeader: typeof import('./src/components/layout/BlogHeader.vue')['default']
    BlogLayout: typeof import('./src/components/layout/BlogLayout.vue')['default']
    BlogSidebar: typeof import('./src/components/layout/BlogSidebar.vue')['default']
    CommentItem: typeof import('./src/components/comment/CommentItem.vue')['default']
    CommentList: typeof import('./src/components/comment/CommentList.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElResult: typeof import('element-plus/es')['ElResult']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    FileUpload: typeof import('./src/components/common/FileUpload.vue')['default']
    Footer: typeof import('./src/components/layout/Footer.vue')['default']
    Header: typeof import('./src/components/layout/Header.vue')['default']
    MainLayout: typeof import('./src/components/layout/MainLayout.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Sidebar: typeof import('./src/components/layout/Sidebar.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
