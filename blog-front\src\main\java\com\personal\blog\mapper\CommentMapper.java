package com.personal.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.personal.blog.entity.Comment;
import com.personal.blog.vo.CommentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 评论Mapper接口
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Mapper
public interface CommentMapper extends BaseMapper<Comment> {

    /**
     * 根据文章ID查询评论列表
     * 
     * @param articleId 文章ID
     * @param currentUserId 当前用户ID（可为null）
     * @return 评论列表
     */
    List<CommentVO> selectCommentsByArticleId(@Param("articleId") Long articleId, @Param("currentUserId") Long currentUserId);

    /**
     * 查询用户评论列表
     * 
     * @param page 分页参数
     * @param userId 用户ID
     * @return 评论列表
     */
    IPage<CommentVO> selectUserComments(Page<CommentVO> page, @Param("userId") Long userId);
}
