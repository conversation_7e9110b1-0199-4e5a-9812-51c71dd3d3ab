package com.personal.blog.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.personal.blog.config.JsonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 关于页面信息实体类
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Data
@TableName("tb_about_info")
public class AboutInfo {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @JsonProperty("userId")
    private Long userId;

    /**
     * 标题
     */
    private String title;

    /**
     * 个人描述（JSON数组格式）
     */
    @TableField(value = "description", typeHandler = JsonTypeHandler.class)
    @JsonProperty("description")
    private List<String> description;

    /**
     * 联系邮箱
     */
    @JsonProperty("contactEmail")
    private String contactEmail;

    /**
     * GitHub链接
     */
    @JsonProperty("contactGithub")
    private String contactGithub;

    /**
     * 位置信息
     */
    @JsonProperty("contactLocation")
    private String contactLocation;

    /**
     * 社交链接（JSON数组格式）
     */
    @TableField(value = "social_links", typeHandler = JsonTypeHandler.class)
    @JsonProperty("socialLinks")
    private List<Map<String, Object>> socialLinks;

    /**
     * 前端技能（JSON数组格式）
     */
    @TableField(value = "skills_frontend", typeHandler = JsonTypeHandler.class)
    @JsonProperty("skillsFrontend")
    private List<String> skillsFrontend;

    /**
     * 后端技能（JSON数组格式）
     */
    @TableField(value = "skills_backend", typeHandler = JsonTypeHandler.class)
    @JsonProperty("skillsBackend")
    private List<String> skillsBackend;

    /**
     * 数据库技能（JSON数组格式）
     */
    @TableField(value = "skills_database", typeHandler = JsonTypeHandler.class)
    @JsonProperty("skillsDatabase")
    private List<String> skillsDatabase;

    /**
     * 工具技能（JSON数组格式）
     */
    @TableField(value = "skills_tools", typeHandler = JsonTypeHandler.class)
    @JsonProperty("skillsTools")
    private List<String> skillsTools;

    /**
     * 创建时间
     */
    @JsonProperty("createTime")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonProperty("updateTime")
    private LocalDateTime updateTime;
} 