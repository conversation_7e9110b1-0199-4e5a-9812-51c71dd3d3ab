import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import './assets/styles/global.css'
import { useUserStore } from '@/store/user'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(createPinia())
app.use(router)
app.use(ElementPlus, {
  zIndex: 3000,
  locale: zhCn
})

// 初始化用户信息
const userStore = useUserStore()
userStore.initUser()

app.mount('#app')