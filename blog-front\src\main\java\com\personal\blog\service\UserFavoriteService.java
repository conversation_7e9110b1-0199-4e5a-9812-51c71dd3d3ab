package com.personal.blog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.personal.blog.entity.UserFavorite;
import com.personal.blog.vo.ArticleVO;
import com.personal.blog.vo.PageResult;

/**
 * 用户收藏Service接口
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
public interface UserFavoriteService extends IService<UserFavorite> {

    /**
     * 收藏文章
     * 
     * @param articleId 文章ID
     * @return 是否收藏成功
     */
    boolean favoriteArticle(Long articleId);

    /**
     * 取消收藏
     * 
     * @param articleId 文章ID
     * @return 是否取消成功
     */
    boolean unfavoriteArticle(Long articleId);

    /**
     * 检查用户是否已收藏
     * 
     * @param articleId 文章ID
     * @return 是否已收藏
     */
    boolean isFavorited(Long articleId);

    /**
     * 获取用户收藏的文章列表
     * 
     * @param current 当前页
     * @param size 每页大小
     * @return 文章列表
     */
    PageResult<ArticleVO> getUserFavoriteArticles(Integer current, Integer size);

    /**
     * 取消收藏文章
     * 
     * @param articleId 文章ID
     */
    void removeFavorite(Long articleId);
} 