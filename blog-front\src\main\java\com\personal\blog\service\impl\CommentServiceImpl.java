package com.personal.blog.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.personal.blog.dto.CommentCreateDTO;
import com.personal.blog.entity.Article;
import com.personal.blog.entity.Comment;
import com.personal.blog.exception.BusinessException;
import com.personal.blog.mapper.CommentMapper;
import com.personal.blog.entity.CommentLike;
import com.personal.blog.mapper.CommentLikeMapper;
import com.personal.blog.service.ArticleService;
import com.personal.blog.service.CommentService;
import com.personal.blog.utils.IpUtils;
import com.personal.blog.vo.CommentVO;
import com.personal.blog.vo.PageResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 评论Service实现类
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Service
@RequiredArgsConstructor
public class CommentServiceImpl extends ServiceImpl<CommentMapper, Comment> implements CommentService {

    private final ArticleService articleService;
    private final CommentLikeMapper commentLikeMapper;

    /**
     * 获取当前用户ID，如果未登录则返回null
     */
    private Long getCurrentUserId() {
        try {
            return StpUtil.getLoginIdAsLong();
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public CommentVO createComment(CommentCreateDTO commentCreateDTO) {
        Long currentUserId = StpUtil.getLoginIdAsLong();
        
        // 检查文章是否存在
        Article article = articleService.getById(commentCreateDTO.getArticleId());
        if (article == null) {
            throw new BusinessException("文章不存在");
        }
        
        // 如果是回复评论，检查父评论是否存在
        if (commentCreateDTO.getParentId() != null && commentCreateDTO.getParentId() > 0) {
            Comment parentComment = this.getById(commentCreateDTO.getParentId());
            if (parentComment == null) {
                throw new BusinessException("父评论不存在");
            }
        }
        
        // 如果是回复特定评论，检查回复目标是否存在
        if (commentCreateDTO.getReplyToId() != null && commentCreateDTO.getReplyToId() > 0) {
            Comment replyToComment = this.getById(commentCreateDTO.getReplyToId());
            if (replyToComment == null) {
                throw new BusinessException("回复目标评论不存在");
            }
        }
        
        Comment comment = BeanUtil.copyProperties(commentCreateDTO, Comment.class);
        comment.setUserId(currentUserId);
        comment.setStatus(1); // 默认审核通过
        
        // 获取IP地址
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            comment.setIpAddress(IpUtils.getClientIpAddress(request));
        }
        
        this.save(comment);
        
        // 更新文章评论数
        articleService.update(new LambdaUpdateWrapper<Article>()
                .eq(Article::getId, commentCreateDTO.getArticleId())
                .setSql("comment_count = comment_count + 1"));
        
        // 查询并返回评论信息
        List<CommentVO> comments = baseMapper.selectCommentsByArticleId(commentCreateDTO.getArticleId(), getCurrentUserId());
        return comments.stream()
                .filter(c -> c.getId().equals(comment.getId()))
                .findFirst()
                .orElse(null);
    }

    @Override
    public List<CommentVO> getCommentsByArticleId(Long articleId) {
        List<CommentVO> allComments = baseMapper.selectCommentsByArticleId(articleId, getCurrentUserId());
        
        // 构建两层评论结构
        List<CommentVO> rootComments = allComments.stream()
                .filter(comment -> comment.getParentId() == null || comment.getParentId() == 0)
                .collect(Collectors.toList());
        
        // 获取所有回复（第二层评论）
        List<CommentVO> replies = allComments.stream()
                .filter(comment -> comment.getParentId() != null && comment.getParentId() > 0)
                .collect(Collectors.toList());
        
        // 为每个根评论设置回复
        rootComments.forEach(rootComment -> {
            List<CommentVO> commentReplies = replies.stream()
                    .filter(reply -> reply.getParentId().equals(rootComment.getId()))
                    .collect(Collectors.toList());
            
            // 为每个回复设置回复目标信息
            commentReplies.forEach(reply -> {
                if (reply.getReplyToId() != null && reply.getReplyToId() > 0) {
                    // 查找回复目标评论
                    CommentVO replyTarget = allComments.stream()
                            .filter(c -> c.getId().equals(reply.getReplyToId()))
                            .findFirst()
                            .orElse(null);
                    
                    if (replyTarget != null) {
                        // 创建回复对象
                        CommentVO replyTo = new CommentVO();
                        replyTo.setId(replyTarget.getId());
                        replyTo.setUserNickname(replyTarget.getUserNickname());
                        replyTo.setUserAvatar(replyTarget.getUserAvatar());
                        reply.setReplyTo(replyTo);
                        
                        // 设置回复用户信息
                        reply.setReplyUserId(replyTarget.getUserId());
                        reply.setReplyUserNickname(replyTarget.getUserNickname());
                        reply.setReplyUserAvatar(replyTarget.getUserAvatar());
                    }
                }
            });
            
            rootComment.setChildren(commentReplies);
        });
        
        return rootComments;
    }

    @Override
    public void deleteComment(Long id) {
        Comment comment = this.getById(id);
        if (comment == null) {
            throw new BusinessException("评论不存在");
        }
        
        // 检查权限
        Long currentUserId = StpUtil.getLoginIdAsLong();
        if (!comment.getUserId().equals(currentUserId)) {
            throw new BusinessException("无权限删除此评论");
        }
        
        // 计算要删除的评论数
        int deleteCount = 1;
        
        // 如果是一级评论，先统计子评论数量，再删除所有子评论
        if (comment.getParentId() == null || comment.getParentId() == 0) {
            // 先统计子评论数量
            Long childCount = this.count(new LambdaUpdateWrapper<Comment>()
                    .eq(Comment::getParentId, id));
            deleteCount += childCount.intValue();
            
            // 删除所有子评论
            this.remove(new LambdaUpdateWrapper<Comment>()
                    .eq(Comment::getParentId, id));
        }
        
        // 删除当前评论
        this.removeById(id);
        
        // 更新文章评论数
        articleService.update(new LambdaUpdateWrapper<Article>()
                .eq(Article::getId, comment.getArticleId())
                .setSql("comment_count = comment_count - " + deleteCount));
    }

    @Override
    public void likeComment(Long id) {
        Comment comment = this.getById(id);
        if (comment == null) {
            throw new BusinessException("评论不存在");
        }
        
        Long currentUserId = StpUtil.getLoginIdAsLong();
        
        // 检查是否已经点赞
        boolean exists = commentLikeMapper.exists(new LambdaUpdateWrapper<CommentLike>()
                .eq(CommentLike::getCommentId, id)
                .eq(CommentLike::getUserId, currentUserId));
        
        if (exists) {
            throw new BusinessException("您已经点赞过这条评论");
        }
        
        // 创建点赞记录
        CommentLike commentLike = new CommentLike();
        commentLike.setCommentId(id);
        commentLike.setUserId(currentUserId);
        commentLikeMapper.insert(commentLike);
        
        // 更新评论点赞数
        this.update(new LambdaUpdateWrapper<Comment>()
                .eq(Comment::getId, id)
                .setSql("like_count = like_count + 1"));
    }

    @Override
    public void unlikeComment(Long id) {
        Comment comment = this.getById(id);
        if (comment == null) {
            throw new BusinessException("评论不存在");
        }
        
        Long currentUserId = StpUtil.getLoginIdAsLong();
        
        // 删除点赞记录
        int deleted = commentLikeMapper.delete(new LambdaUpdateWrapper<CommentLike>()
                .eq(CommentLike::getCommentId, id)
                .eq(CommentLike::getUserId, currentUserId));
        
        if (deleted == 0) {
            throw new BusinessException("您还没有点赞过这条评论");
        }
        
        // 更新评论点赞数
        this.update(new LambdaUpdateWrapper<Comment>()
                .eq(Comment::getId, id)
                .setSql("like_count = like_count - 1"));
    }

    @Override
    public PageResult<CommentVO> getUserComments(Integer current, Integer size) {
        Long currentUserId = StpUtil.getLoginIdAsLong();
        
        Page<CommentVO> page = new Page<>(current, size);
        IPage<CommentVO> result = baseMapper.selectUserComments(page, currentUserId);
        
        return new PageResult<>(result.getRecords(), result.getTotal(), 
                result.getCurrent(), result.getSize());
    }

    @Override
    public void deleteUserComment(Long id) {
        Comment comment = this.getById(id);
        if (comment == null) {
            throw new BusinessException("评论不存在");
        }
        
        // 检查权限
        Long currentUserId = StpUtil.getLoginIdAsLong();
        if (!comment.getUserId().equals(currentUserId)) {
            throw new BusinessException("无权限删除此评论");
        }
        
        this.removeById(id);
        
        // 更新文章评论数
        articleService.update(new LambdaUpdateWrapper<Article>()
                .eq(Article::getId, comment.getArticleId())
                .setSql("comment_count = comment_count - 1"));
    }
}
