package com.personal.blog.utils;

import com.personal.blog.vo.ArticleVO;
import java.util.List;

/**
 * 文章热度计算工具类
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
public class PopularityCalculator {

    // 权重配置
    private static final double VIEW_WEIGHT = 0.4;    // 访问量权重 40%
    private static final double LIKE_WEIGHT = 0.4;    // 点赞数权重 40%
    private static final double COMMENT_WEIGHT = 0.2; // 评论数权重 20%

    /**
     * 计算单篇文章的综合评分
     * 
     * @param article 文章信息
     * @param maxViewCount 该作者文章最大访问量
     * @param maxLikeCount 该作者文章最大点赞数
     * @param maxCommentCount 该作者文章最大评论数
     * @return 综合评分
     */
    public static double calculateScore(ArticleVO article, int maxViewCount, int maxLikeCount, int maxCommentCount) {
        if (maxViewCount == 0 && maxLikeCount == 0 && maxCommentCount == 0) {
            return 0.0;
        }

        // 标准化处理，避免除零错误
        double viewScore = maxViewCount > 0 ? (double) article.getViewCount() / maxViewCount : 0;
        double likeScore = maxLikeCount > 0 ? (double) article.getLikeCount() / maxLikeCount : 0;
        double commentScore = maxCommentCount > 0 ? (double) article.getCommentCount() / maxCommentCount : 0;

        // 计算加权评分
        return viewScore * VIEW_WEIGHT + likeScore * LIKE_WEIGHT + commentScore * COMMENT_WEIGHT;
    }

    /**
     * 计算作者所有文章的综合评分并排序
     * 
     * @param articles 文章列表
     * @return 排序后的文章列表
     */
    public static List<ArticleVO> calculateAndSortArticles(List<ArticleVO> articles) {
        if (articles == null || articles.isEmpty()) {
            return articles;
        }

        // 找出该作者文章中的最大值
        int maxViewCount = articles.stream()
                .mapToInt(article -> article.getViewCount() != null ? article.getViewCount() : 0)
                .max()
                .orElse(0);

        int maxLikeCount = articles.stream()
                .mapToInt(article -> article.getLikeCount() != null ? article.getLikeCount() : 0)
                .max()
                .orElse(0);

        int maxCommentCount = articles.stream()
                .mapToInt(article -> article.getCommentCount() != null ? article.getCommentCount() : 0)
                .max()
                .orElse(0);

        // 计算每篇文章的综合评分
        articles.forEach(article -> {
            double score = calculateScore(article, maxViewCount, maxLikeCount, maxCommentCount);
            article.setPopularityScore(score);
        });

        // 按综合评分排序（降序）
        articles.sort((a, b) -> Double.compare(b.getPopularityScore(), a.getPopularityScore()));

        return articles;
    }

    /**
     * 获取作者热门文章（前N篇）
     * 
     * @param articles 文章列表
     * @param limit 限制数量
     * @return 热门文章列表
     */
    public static List<ArticleVO> getPopularArticles(List<ArticleVO> articles, int limit) {
        List<ArticleVO> sortedArticles = calculateAndSortArticles(articles);
        return sortedArticles.stream()
                .limit(limit)
                .toList();
    }
} 