-- 修复关于页面信息数据
USE personal_blog;

-- 先删除现有数据
DELETE FROM tb_about_info WHERE id > 0;

-- 重新插入数据
INSERT INTO `tb_about_info` (
  `user_id`,
  `title`, 
  `description`, 
  `contact_email`, 
  `contact_github`, 
  `contact_location`,
  `social_links`,
  `skills_frontend`,
  `skills_backend`,
  `skills_database`,
  `skills_tools`
) VALUES (
  1,
  '全栈开发工程师 | 技术博主 | 开源爱好者',
  JSON_ARRAY(
    '你好！我是一名热爱技术的全栈开发工程师，专注于Web开发和技术分享。',
    '拥有多年的开发经验，熟悉前端和后端技术栈。',
    '我喜欢学习新技术，探索最佳实践，并通过博客分享我的经验和见解。',
    '希望能够帮助更多的开发者成长，也期待与大家交流学习。'
  ),
  '<EMAIL>',
  'github.com/admin',
  '中国，北京',
  JSON_ARRAY(
    JSON_OBJECT('name', 'GitHub', 'icon', 'Link', 'url', 'https://github.com/admin'),
    JSON_OBJECT('name', 'Email', 'icon', 'Message', 'url', 'mailto:<EMAIL>'),
    JSON_OBJECT('name', '微博', 'icon', 'Share', 'url', 'https://weibo.com/admin')
  ),
  JSON_ARRAY('Vue.js', 'React', 'JavaScript', 'TypeScript', 'HTML5', 'CSS3', 'Sass', 'Element Plus'),
  JSON_ARRAY('Java', 'Spring Boot', 'Node.js', 'Express', 'Python', 'Django', 'RESTful API'),
  JSON_ARRAY('MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'Elasticsearch'),
  JSON_ARRAY('Git', 'Docker', 'Linux', 'Nginx', 'Webpack', 'Vite', 'Jenkins')
);

-- 验证数据
SELECT * FROM tb_about_info; 