package com.personal.blog.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * JSON类型处理器 - 专门处理MySQL JSON类型
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@MappedTypes({List.class, Map.class})
@MappedJdbcTypes({JdbcType.VARCHAR, JdbcType.LONGVARCHAR})
public class JsonTypeHandler extends BaseTypeHandler<Object> {
    
    private static final ObjectMapper MAPPER = new ObjectMapper();
    
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType) throws SQLException {
        if (parameter == null) {
            ps.setNull(i, jdbcType.TYPE_CODE);
        } else {
            ps.setString(i, toJson(parameter));
        }
    }
    
    @Override
    public Object getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        return fromJson(value);
    }
    
    @Override
    public Object getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String value = rs.getString(columnIndex);
        return fromJson(value);
    }
    
    @Override
    public Object getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String value = cs.getString(columnIndex);
        return fromJson(value);
    }
    
    private String toJson(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return MAPPER.writeValueAsString(obj);
        } catch (Exception e) {
            throw new RuntimeException("JSON序列化失败: " + e.getMessage(), e);
        }
    }
    
    private Object fromJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        
        // 移除可能的BLOB标记
        if (json.contains("<<BLOB>>")) {
            return null;
        }
        
        try {
            // 检查是否是JSON数组格式（以[开头，以]结尾）
            if (json.trim().startsWith("[") && json.trim().endsWith("]")) {
                return MAPPER.readValue(json, List.class);
            }
            // 检查是否是JSON对象格式（以{开头，以}结尾）
            else if (json.trim().startsWith("{") && json.trim().endsWith("}")) {
                return MAPPER.readValue(json, Map.class);
            }
            // 如果不是标准JSON格式，尝试解析为Object
            else {
                return MAPPER.readValue(json, Object.class);
            }
        } catch (Exception e) {
            // 如果JSON解析失败，返回原始字符串
            return json;
        }
    }
} 