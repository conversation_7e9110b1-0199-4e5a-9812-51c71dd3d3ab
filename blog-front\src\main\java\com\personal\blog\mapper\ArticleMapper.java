package com.personal.blog.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.personal.blog.dto.ArticleQueryDTO;
import com.personal.blog.entity.Article;
import com.personal.blog.entity.UserFavorite;
import com.personal.blog.entity.UserLike;
import com.personal.blog.vo.ArticleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文章Mapper接口
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
@Mapper
public interface ArticleMapper extends BaseMapper<Article> {

    /**
     * 分页查询文章列表
     * 
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 文章列表
     */
    IPage<ArticleVO> selectArticlePage(Page<ArticleVO> page, @Param("query") ArticleQueryDTO queryDTO);

    /**
     * 根据ID查询文章详情
     * 
     * @param id 文章ID
     * @return 文章详情
     */
    ArticleVO selectArticleById(@Param("id") Long id);

    /**
     * 查询用户点赞记录
     * 
     * @param userId 用户ID
     * @param articleId 文章ID
     * @return 点赞记录
     */
    UserLike selectUserLike(@Param("userId") Long userId, @Param("articleId") Long articleId);

    /**
     * 查询用户收藏记录
     * 
     * @param userId 用户ID
     * @param articleId 文章ID
     * @return 收藏记录
     */
    UserFavorite selectUserFavorite(@Param("userId") Long userId, @Param("articleId") Long articleId);

    /**
     * 查询用户文章列表
     * 
     * @param page 分页参数
     * @param userId 用户ID
     * @return 文章列表
     */
    IPage<ArticleVO> selectUserArticles(Page<ArticleVO> page, @Param("userId") Long userId);

    /**
     * 统计总访问量
     * 
     * @return 总访问量
     */
    Integer selectTotalViewCount();
    
    /**
     * 统计作者文章总访问量
     * 
     * @param authorId 作者ID
     * @return 作者文章总访问量
     */
    Integer selectAuthorTotalViewCount(@Param("authorId") Long authorId);

    /**
     * 查询作者所有已发布的文章
     * 
     * @param authorId 作者ID
     * @return 文章列表
     */
    List<ArticleVO> selectAuthorArticles(@Param("authorId") Long authorId);
}
