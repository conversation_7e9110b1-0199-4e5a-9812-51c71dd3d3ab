<template>
  <div class="comment-list">
    <div class="comment-header">
      <h3 class="comment-title">
        <el-icon><ChatDotRound /></el-icon>
        评论 ({{ totalComments }})
      </h3>
    </div>

    <!-- 评论表单 -->
    <div class="comment-form" v-if="userStore.isLoggedIn">
      <div class="form-header">
        <img :src="getAvatarUrl(userStore.avatar)" :alt="userStore.nickname" class="user-avatar">
        <span class="user-name">{{ userStore.nickname }}</span>
      </div>
      <el-input
        v-model="commentContent"
        type="textarea"
        :rows="4"
        :placeholder="replyTo ? `回复 @${replyTo.userNickname}：` : '写下你的评论...'"
        maxlength="500"
        show-word-limit
      />
      <div class="form-actions">
        <el-button v-if="replyTo" type="default" @click="cancelReply">
          取消回复
        </el-button>
        <el-button type="primary" @click="submitComment" :loading="submitting">
          {{ replyTo ? '回复' : '发表评论' }}
        </el-button>
      </div>
    </div>

    <!-- 登录提示 -->
    <div class="login-tip" v-else>
      <p>
        <el-button link @click="$router.push('/login')">登录</el-button>
        后参与评论
      </p>
    </div>

    <!-- 评论列表 -->
    <div class="comments" v-loading="loading">
      <CommentItem
        v-for="comment in comments"
        :key="comment.id"
        :comment="comment"
        :depth="0"
        @reply="handleReply"
        @like="handleLike"
        @delete="handleDelete"
      />

      <!-- 空状态 -->
      <div v-if="!comments.length && !loading" class="empty-comments">
        <el-empty description="暂无评论，快来抢沙发吧！" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useUserStore } from '@/store/user'
import { useArticleStore } from '@/store/article'
import { getArticleComments, createComment, deleteComment, likeComment, unlikeComment } from '@/api/comment'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import CommentItem from './CommentItem.vue'
import { getAvatarUrl } from '@/utils/avatar'

const props = defineProps({
  articleId: {
    type: [String, Number],
    required: true
  }
})

const userStore = useUserStore()
const articleStore = useArticleStore()

const comments = ref([])
const commentContent = ref('')
const loading = ref(false)
const submitting = ref(false)
const replyTo = ref(null) // 当前回复的评论

// 格式化日期
const formatDate = (date) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 计算总评论数（包括回复）
const totalComments = computed(() => {
  let count = comments.value.length
  comments.value.forEach(comment => {
    if (comment.children && comment.children.length > 0) {
      count += comment.children.length
    }
  })
  return count
})

// 获取评论列表
const fetchComments = async () => {
  try {
    loading.value = true
    const response = await getArticleComments(props.articleId)
    comments.value = response.data || []
  } catch (error) {
    console.error('获取评论失败:', error)
  } finally {
    loading.value = false
  }
}

// 提交评论
const submitComment = async () => {
  if (!commentContent.value.trim()) {
    ElMessage.warning('请输入评论内容')
    return
  }

  try {
    submitting.value = true
    const commentData = {
      articleId: props.articleId,
      content: commentContent.value.trim()
    }
    
    // 如果是回复评论，添加父评论ID和回复目标ID
    if (replyTo.value) {
      // 如果回复的是主评论，parentId 和 replyToId 都是主评论的ID
      if (replyTo.value.parentId === 0 || !replyTo.value.parentId) {
        commentData.parentId = replyTo.value.id
        commentData.replyToId = replyTo.value.id
      } else {
        // 如果回复的是回复，parentId 是主评论ID，replyToId 是当前回复的ID
        commentData.parentId = replyTo.value.parentId
        commentData.replyToId = replyTo.value.id
      }
    }
    
    await createComment(commentData)
    
    ElMessage.success(replyTo.value ? '回复成功' : '评论发表成功')
    commentContent.value = ''
    replyTo.value = null // 清除回复状态
    
    // 更新文章评论数
    if (articleStore.currentArticle.id == props.articleId) {
      articleStore.currentArticle.commentCount = (articleStore.currentArticle.commentCount || 0) + 1
    }
    
    // 重新获取评论列表
    await fetchComments()
    
    await fetchComments() // 重新获取评论列表
  } catch (error) {
    console.error('提交评论失败:', error)
    ElMessage.error(replyTo.value ? '回复失败' : '评论发表失败')
  } finally {
    submitting.value = false
  }
}

// 处理回复
const handleReply = (comment) => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    return
  }
  
  replyTo.value = comment
  // 聚焦到评论输入框
  const textarea = document.querySelector('.comment-form textarea')
  if (textarea) {
    textarea.focus()
  }
}

// 处理点赞
const handleLike = async (comment) => {
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    return
  }

  try {
    if (comment.isLiked) {
      // 取消点赞
      await unlikeComment(comment.id)
      comment.likeCount = Math.max(0, (comment.likeCount || 0) - 1)
      comment.isLiked = false
      ElMessage.success('取消点赞成功')
    } else {
      // 点赞
      await likeComment(comment.id)
      comment.likeCount = (comment.likeCount || 0) + 1
      comment.isLiked = true
      ElMessage.success('点赞成功')
    }
  } catch (error) {
    console.error('点赞操作失败:', error)
    ElMessage.error(error.response?.data?.message || '操作失败')
  }
}

// 处理删除评论
const handleDelete = async (comment) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条评论吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await deleteComment(comment.id)
    ElMessage.success('评论删除成功')
    
    // 更新文章评论数
    if (articleStore.currentArticle.id == props.articleId) {
      // 计算删除的评论数（包括子评论）
      let deleteCount = 1
      if (comment.children && comment.children.length > 0) {
        deleteCount += comment.children.length
      }
      articleStore.currentArticle.commentCount = Math.max(0, (articleStore.currentArticle.commentCount || 0) - deleteCount)
    }
    
    // 重新获取评论列表
    await fetchComments()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除评论失败:', error)
      ElMessage.error('删除评论失败')
    }
  }
}

// 取消回复
const cancelReply = () => {
  replyTo.value = null
  commentContent.value = ''
}

onMounted(() => {
  fetchComments()
})
</script>

<style scoped>
.comment-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.comment-header {
  padding-bottom: var(--spacing-md);
  border-bottom: 2px solid var(--border-light);
}

.comment-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--primary-color);
  margin: 0;
}

.reply-count {
  font-size: var(--font-sm);
  font-weight: 400;
  color: var(--text-secondary);
}



.comment-form {
  background: var(--bg-gray);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.form-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-weight: 500;
  color: var(--primary-color);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
}

.login-tip {
  text-align: center;
  padding: var(--spacing-lg);
  background: var(--bg-gray);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
}

.comments {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.empty-comments {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 移动端样式 */
@media (max-width: 768px) {
  .comment-form {
    padding: var(--spacing-md);
  }
}
</style>
