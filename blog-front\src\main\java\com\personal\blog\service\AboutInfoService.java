package com.personal.blog.service;

import com.personal.blog.dto.AboutUpdateDTO;
import com.personal.blog.entity.AboutInfo;

import java.util.Map;

/**
 * 关于页面信息 Service 接口
 * 
 * <AUTHOR> Blog
 * @since 2024-01-01
 */
public interface AboutInfoService {

    /**
     * 获取关于页面信息
     */
    Map<String, Object> getAboutInfo();

    /**
     * 更新关于页面信息
     */
    Map<String, Object> updateAboutInfo(AboutUpdateDTO aboutUpdateDTO);

    /**
     * 获取关于页面信息实体
     */
    AboutInfo getAboutInfoEntity();
} 