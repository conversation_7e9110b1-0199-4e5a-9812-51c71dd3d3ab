import { request } from '@/utils/request'

/**
 * 文件上传相关 API
 */

// 上传头像
export const uploadAvatar = (file) => {
  const formData = new FormData()
  formData.append('file', file)
  return request.upload('/files/avatar', formData)
}

// 上传文章图片
export const uploadArticleImage = (file) => {
  const formData = new FormData()
  formData.append('file', file)
  return request.upload('/files/article-image', formData)
}

// 上传通用文件
export const uploadFile = (file) => {
  const formData = new FormData()
  formData.append('file', file)
  return request.upload('/files/upload', formData)
}

// 删除文件
export const deleteFile = (fileUrl) => {
  return request.delete('/files/delete', {
    params: { fileUrl }
  })
} 