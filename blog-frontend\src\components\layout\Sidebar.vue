<template>
  <aside v-if="showSidebar" class="sidebar">
    <!-- 作者简介 -->
    <div class="sidebar-card">
      <div class="profile-section">
        <div class="avatar-container">
          <img :src="getAvatarUrl(authorInfo.avatar)" alt="作者头像" class="profile-avatar">
        </div>
        <h3 class="profile-name">{{ authorInfo.name || '作者昵称' }}</h3>
        <p class="profile-desc">{{ authorInfo.title || '热爱技术，喜欢分享，专注于前端开发和全栈技术。' }}</p>
        <div class="profile-stats">
          <div class="stat-item">
            <span class="stat-number">{{ authorStats.articleCount }}</span>
            <span class="stat-label">文章</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ authorStats.viewCount }}</span>
            <span class="stat-label">访问</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ authorStats.commentCount }}</span>
            <span class="stat-label">评论</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 作者热门文章 -->
    <div class="sidebar-card">
      <h3 class="card-title">
        <el-icon><Document /></el-icon>
        作者热门文章
      </h3>
      <div class="author-articles" v-loading="loading">
        <div 
          v-for="(article, index) in authorArticles" 
          :key="article.id"
          class="article-item"
          @click="handleArticleClick(article.id)"
        >
          <div class="article-info">
            <h4 class="article-title">{{ article.title }}</h4>
            <div class="article-meta">
              <span class="like-count">
                <el-icon><Star /></el-icon>
                {{ article.likeCount || 0 }}
              </span>
              <span class="view-count">
                <el-icon><View /></el-icon>
                {{ article.viewCount }}
              </span>
              <span class="comment-count">
                <el-icon><ChatDotRound /></el-icon>
                {{ article.commentCount || 0 }}
              </span>
              <span class="publish-time">{{ formatDate(article.createTime) }}</span>
            </div>
          </div>
        </div>
        <div v-if="!authorArticles.length && !loading" class="empty-state">
          {{ authorArticles.length === 0 ? '暂无其他文章' : '暂无热门文章' }}
        </div>
      </div>
    </div>

    <!-- 文章分类 -->
    <div class="sidebar-card">
      <h3 class="card-title">
        <el-icon><Folder /></el-icon>
        文章分类
      </h3>
      <div class="categories" v-loading="articleStore.loading">
        <div 
          v-for="category in articleStore.categories" 
          :key="category.id"
          class="category-item"
          @click="$router.push(`/category/${category.id}`)"
        >
          <span class="category-name">{{ category.name }}</span>
          <span class="category-count">{{ category.articleCount }}</span>
        </div>
        <div v-if="!articleStore.categories.length && !articleStore.loading" class="empty-state">
          暂无分类
        </div>
      </div>
    </div>

    <!-- 标签云 -->
    <div class="sidebar-card">
      <h3 class="card-title">
        <el-icon><CollectionTag /></el-icon>
        标签云
      </h3>
      <div class="tag-cloud">
        <span 
          v-for="tag in tags" 
          :key="tag.id"
          class="tag-item"
          :style="{ fontSize: getTagSize(tag.count) }"
          @click="handleTagClick(tag)"
        >
          {{ tag.name }}
        </span>
        <div v-if="!tags.length" class="empty-state">
          暂无标签
        </div>
      </div>
    </div>
  </aside>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useArticleStore } from '@/store/article'
import { getAboutInfo } from '@/api/about'
import { getStatistics, getAuthorStatistics } from '@/api/statistics'
import { getAuthorArticles } from '@/api/article'
import { Document, Star, ChatDotRound } from '@element-plus/icons-vue'
import { getAvatarUrl } from '@/utils/avatar'

const router = useRouter()
const route = useRoute()
const articleStore = useArticleStore()

// 是否显示侧边栏（只在文章详情页面显示）
const showSidebar = computed(() => {
  return route.path.startsWith('/article/') && route.params.id
})

// 作者信息
const authorInfo = ref({
  name: '',
  title: '',
  avatar: '',
  description: []
})

// 作者统计数据
const authorStats = ref({
  articleCount: 0,
  viewCount: 0,
  commentCount: 0
})

// 作者其他文章
const authorArticles = ref([])
const loading = ref(false)

// 标签数据
const tags = ref([
  { id: 1, name: 'Vue.js', count: 15 },
  { id: 2, name: 'JavaScript', count: 20 },
  { id: 3, name: 'CSS', count: 12 },
  { id: 4, name: 'Node.js', count: 8 },
  { id: 5, name: 'Spring Boot', count: 10 },
  { id: 6, name: 'MySQL', count: 6 },
  { id: 7, name: 'Redis', count: 4 },
  { id: 8, name: 'Docker', count: 5 }
])

// 获取标签字体大小
const getTagSize = (count) => {
  const minSize = 12
  const maxSize = 18
  const maxCount = Math.max(...tags.value.map(tag => tag.count))
  const size = minSize + (count / maxCount) * (maxSize - minSize)
  return `${size}px`
}

// 标签点击处理
const handleTagClick = (tag) => {
  router.push({
    path: '/search',
    query: { tag: tag.name }
  })
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// 处理文章点击
const handleArticleClick = async (articleId) => {
  // 先跳转路由
  await router.push(`/article/${articleId}`)
  
  // 等待一下让路由变化生效
  await new Promise(resolve => setTimeout(resolve, 100))
  
  // 手动触发数据更新
  const currentArticle = articleStore.currentArticle
  if (currentArticle && currentArticle.authorId) {
    await fetchAuthorInfo(currentArticle.authorId)
    await fetchAuthorStats(currentArticle.authorId)
    await fetchAuthorArticles(currentArticle.authorId, currentArticle.id)
  }
}

// 获取作者信息
const fetchAuthorInfo = async (authorId) => {
  try {
    // 直接从当前文章数据中获取作者信息
    const currentArticle = articleStore.currentArticle
    if (currentArticle) {
      authorInfo.value = {
        name: currentArticle.authorNickname,
        title: currentArticle.title,
        avatar: currentArticle.authorAvatar,
        description: []
      }
    }
  } catch (error) {
    console.error('获取作者信息失败:', error)
  }
}

// 获取作者统计数据
const fetchAuthorStats = async (authorId) => {
  try {
    const response = await getAuthorStatistics(authorId)
    if (response.data) {
      authorStats.value = response.data
    }
  } catch (error) {
    console.error('获取作者统计数据失败:', error)
  }
}

// 获取作者热门文章
const fetchAuthorArticles = async (authorId, currentArticleId) => {
  if (!authorId) return
  
  loading.value = true
  try {
    const response = await getAuthorArticles(authorId + '/popular?limit=3')
    if (response.data) {
      // 过滤掉当前文章
      authorArticles.value = response.data.filter(article => article.id != currentArticleId)
    }
  } catch (error) {
    console.error('获取作者热门文章失败:', error)
    // 如果热门文章接口失败，回退到普通文章列表
  } finally {
    loading.value = false
  }
}

// 监听路由变化，重新获取作者信息
watch(() => route.params.id, async (newArticleId) => {
  console.log('路由变化，文章ID:', newArticleId)
  if (newArticleId && showSidebar.value) {
    // 等待文章数据加载完成
    await articleStore.fetchArticleDetail(newArticleId)
    
    const currentArticle = articleStore.currentArticle
    if (currentArticle && currentArticle.authorId) {
      await fetchAuthorInfo(currentArticle.authorId)
      await fetchAuthorStats(currentArticle.authorId)
      await fetchAuthorArticles(currentArticle.authorId, currentArticle.id)
    }
  }
}, { immediate: true })

// 监听当前文章变化
watch(() => articleStore.currentArticle, async (newArticle) => {
  console.log('文章数据变化:', newArticle)
  if (newArticle && newArticle.authorId && showSidebar.value) {
    await fetchAuthorInfo(newArticle.authorId)
    await fetchAuthorStats(newArticle.authorId)
    await fetchAuthorArticles(newArticle.authorId, newArticle.id)
  }
}, { deep: true })

// 监听文章评论数变化，更新作者统计数据
watch(() => articleStore.currentArticle.commentCount, async (newCommentCount, oldCommentCount) => {
  if (articleStore.currentArticle.authorId && showSidebar.value && newCommentCount > oldCommentCount) {
    // 只有当评论数增加时才更新作者统计数据
    authorStats.value.commentCount = (authorStats.value.commentCount || 0) + (newCommentCount - oldCommentCount)
  }
})

// 初始化数据
onMounted(async () => {
  // 获取分类列表
  await articleStore.fetchCategories()
})
</script>

<style scoped>
.sidebar {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.sidebar-card {
  background: var(--bg-white);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  transition: box-shadow 0.3s ease;
}

.sidebar-card:hover {
  box-shadow: var(--shadow-md);
}

.card-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--border-light);
}

.profile-section {
  text-align: center;
}

.avatar-container {
  margin-bottom: var(--spacing-md);
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--accent-color);
}

.profile-name {
  font-size: var(--font-xl);
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: var(--spacing-sm);
}

.profile-desc {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
}

.profile-stats {
  display: flex;
  justify-content: space-around;
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--border-light);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.stat-number {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--accent-color);
}

.stat-label {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.hot-articles {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.hot-article-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: background 0.3s ease;
}

.hot-article-item:hover {
  background: var(--bg-gray);
}

.article-rank {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  font-size: var(--font-xs);
  font-weight: 600;
  color: white;
  flex-shrink: 0;
}

.rank-1 { background: #f56565; }
.rank-2 { background: #ed8936; }
.rank-3 { background: #ecc94b; }
.article-rank:not(.rank-1):not(.rank-2):not(.rank-3) {
  background: var(--text-light);
}

.author-articles {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.article-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: background 0.3s ease;
}

.article-item:hover {
  background: var(--bg-gray);
}

.article-info {
  flex: 1;
  min-width: 0;
}

.article-title {
  font-size: var(--font-sm);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-xs);
  color: var(--text-secondary);
}

.like-count {
  display: flex;
  align-items: center;
  gap: 2px;
  color: var(--accent-color);
  font-weight: 500;
}

.view-count {
  display: flex;
  align-items: center;
  gap: 2px;
}

.comment-count {
  display: flex;
  align-items: center;
  gap: 2px;
  color: var(--text-secondary);
}

.publish-time {
  color: var(--text-light);
}

.categories {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-item:hover {
  background: var(--accent-color);
  color: white;
  transform: translateX(4px);
}

.category-name {
  font-weight: 500;
}

.category-count {
  background: var(--bg-gray);
  color: var(--text-secondary);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: var(--font-xs);
  transition: all 0.3s ease;
}

.category-item:hover .category-count {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.tag-item {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--bg-gray);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.tag-item:hover {
  background: var(--accent-color);
  color: white;
  transform: translateY(-2px);
}

.empty-state {
  text-align: center;
  color: var(--text-secondary);
  font-size: var(--font-sm);
  padding: var(--spacing-lg);
}

/* 移动端样式 */
@media (max-width: 768px) {
  .sidebar {
    gap: var(--spacing-md);
  }

  .sidebar-card {
    padding: var(--spacing-md);
  }

  .profile-avatar {
    width: 60px;
    height: 60px;
  }
}
</style>
