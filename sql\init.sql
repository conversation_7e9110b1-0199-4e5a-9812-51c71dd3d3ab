-- 创建数据库
CREATE DATABASE IF NOT EXISTS personal_blog DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE personal_blog;

-- 用户表
CREATE TABLE `tb_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(50) COMMENT '昵称',
  `avatar` varchar(255) COMMENT '头像URL',
  `role` varchar(20) DEFAULT 'USER' COMMENT '角色',
  `status` tinyint DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 分类表
CREATE TABLE `tb_category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `parent_id` bigint DEFAULT 0 COMMENT '父分类ID',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类表';

-- 文章表
CREATE TABLE `tb_article` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文章ID',
  `title` varchar(255) NOT NULL COMMENT '文章标题',
  `summary` text COMMENT '文章摘要',
  `content` longtext NOT NULL COMMENT '文章内容',
  `cover_image` varchar(255) COMMENT '封面图片',
  `category_id` bigint COMMENT '分类ID',
  `author_id` bigint NOT NULL COMMENT '作者ID',
  `view_count` int DEFAULT 0 COMMENT '浏览量',
  `like_count` int DEFAULT 0 COMMENT '点赞数',
  `comment_count` int DEFAULT 0 COMMENT '评论数',
  `is_published` tinyint DEFAULT 0 COMMENT '是否发布：1-已发布，0-草稿',
  `is_top` tinyint DEFAULT 0 COMMENT '是否置顶',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章表';

-- 评论表
CREATE TABLE `tb_comment` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `parent_id` bigint DEFAULT 0 COMMENT '父评论ID（第一层评论ID）',
  `reply_to_id` bigint DEFAULT 0 COMMENT '回复目标评论ID（第二层回复的目标）',
  `content` text NOT NULL COMMENT '评论内容',
  `ip_address` varchar(50) COMMENT 'IP地址',
  `like_count` int DEFAULT 0 COMMENT '点赞数',
  `status` tinyint DEFAULT 1 COMMENT '状态：1-正常，0-待审核，-1-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_article_id` (`article_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_reply_to_id` (`reply_to_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';

-- 评论点赞表
CREATE TABLE `tb_comment_like` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `comment_id` bigint NOT NULL COMMENT '评论ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_comment_user` (`comment_id`, `user_id`),
  KEY `idx_comment_id` (`comment_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论点赞表';

-- 角色表
CREATE TABLE `tb_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_code` varchar(50) NOT NULL COMMENT '角色编码',
  `role_name` varchar(100) NOT NULL COMMENT '角色名称',
  `description` text COMMENT '角色描述',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`role_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 权限表
CREATE TABLE `tb_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `permission_code` varchar(100) NOT NULL COMMENT '权限编码',
  `permission_name` varchar(100) NOT NULL COMMENT '权限名称',
  `permission_type` tinyint DEFAULT 1 COMMENT '权限类型：1-菜单，2-按钮，3-接口',
  `parent_id` bigint DEFAULT 0 COMMENT '父权限ID',
  `permission_path` varchar(255) COMMENT '权限路径',
  `description` text COMMENT '权限描述',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_permission_code` (`permission_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- 用户角色关联表
CREATE TABLE `tb_user_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 角色权限关联表
CREATE TABLE `tb_role_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `permission_id` bigint NOT NULL COMMENT '权限ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- 字典类型表
CREATE TABLE `tb_dict_type` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典类型ID',
  `dict_type_code` varchar(100) NOT NULL COMMENT '字典类型编码',
  `dict_type_name` varchar(100) NOT NULL COMMENT '字典类型名称',
  `description` text COMMENT '字典类型描述',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_dict_type_code` (`dict_type_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字典类型表';

-- 字典数据表
CREATE TABLE `tb_dict_data` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典数据ID',
  `dict_type_code` varchar(100) NOT NULL COMMENT '字典类型编码',
  `dict_label` varchar(100) NOT NULL COMMENT '字典标签',
  `dict_value` varchar(100) NOT NULL COMMENT '字典值',
  `description` text COMMENT '字典描述',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `is_default` tinyint DEFAULT 0 COMMENT '是否默认：1-是，0-否',
  `css_class` varchar(100) COMMENT 'CSS样式类',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_dict_type_code` (`dict_type_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='字典数据表';

-- 系统配置表
CREATE TABLE `tb_system_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `description` text COMMENT '配置描述',
  `config_type` tinyint DEFAULT 1 COMMENT '配置类型：1-系统配置，2-业务配置',
  `is_builtin` tinyint DEFAULT 0 COMMENT '是否内置：1-是，0-否',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 标签表
CREATE TABLE `tb_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `tag_name` varchar(50) NOT NULL COMMENT '标签名称',
  `tag_color` varchar(20) DEFAULT '#409EFF' COMMENT '标签颜色',
  `description` text COMMENT '标签描述',
  `use_count` int DEFAULT 0 COMMENT '使用次数',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tag_name` (`tag_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签表';

-- 文章标签关联表
CREATE TABLE `tb_article_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `tag_id` bigint NOT NULL COMMENT '标签ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_article_id` (`article_id`),
  KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章标签关联表';

-- 用户点赞记录表
CREATE TABLE `tb_user_like` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `status` tinyint DEFAULT 1 COMMENT '点赞状态：1-已点赞，0-取消点赞',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_article` (`user_id`, `article_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_article_id` (`article_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户点赞记录表';

-- 用户收藏记录表
CREATE TABLE `tb_user_favorite` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `article_id` bigint NOT NULL COMMENT '文章ID',
  `status` tinyint DEFAULT 1 COMMENT '收藏状态：1-已收藏，0-取消收藏',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_article` (`user_id`, `article_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_article_id` (`article_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏记录表';

-- 访问日志表
CREATE TABLE `tb_access_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint COMMENT '用户ID',
  `ip_address` varchar(50) COMMENT '访问IP',
  `location` varchar(100) COMMENT '访问地址',
  `request_url` varchar(500) COMMENT '请求URL',
  `request_method` varchar(10) COMMENT '请求方法',
  `user_agent` text COMMENT '用户代理',
  `device_type` tinyint DEFAULT 1 COMMENT '设备类型：1-PC，2-移动端，3-小程序',
  `operating_system` varchar(50) COMMENT '操作系统',
  `browser` varchar(50) COMMENT '浏览器',
  `access_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_access_time` (`access_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='访问日志表';

-- ========================================
-- 插入初始数据
-- ========================================

-- 插入默认角色
INSERT INTO `tb_role` (`role_code`, `role_name`, `description`, `sort_order`, `status`) VALUES
('ADMIN', '超级管理员', '系统超级管理员，拥有所有权限', 1, 1),
('EDITOR', '编辑员', '内容编辑员，可以管理文章和评论', 2, 1),
('USER', '普通用户', '普通注册用户', 3, 1);

-- 插入默认权限
INSERT INTO `tb_permission` (`permission_code`, `permission_name`, `permission_type`, `parent_id`, `permission_path`, `description`, `sort_order`, `status`) VALUES
('SYSTEM', '系统管理', 1, 0, '/admin/system', '系统管理菜单', 1, 1),
('SYSTEM:USER', '用户管理', 1, 1, '/admin/system/user', '用户管理页面', 1, 1),
('SYSTEM:ROLE', '角色管理', 1, 1, '/admin/system/role', '角色管理页面', 2, 1),
('SYSTEM:PERMISSION', '权限管理', 1, 1, '/admin/system/permission', '权限管理页面', 3, 1),
('CONTENT', '内容管理', 1, 0, '/admin/content', '内容管理菜单', 2, 1),
('CONTENT:ARTICLE', '文章管理', 1, 5, '/admin/content/article', '文章管理页面', 1, 1),
('CONTENT:CATEGORY', '分类管理', 1, 5, '/admin/content/category', '分类管理页面', 2, 1),
('CONTENT:TAG', '标签管理', 1, 5, '/admin/content/tag', '标签管理页面', 3, 1),
('CONTENT:COMMENT', '评论管理', 1, 5, '/admin/content/comment', '评论管理页面', 4, 1);

-- 插入管理员用户（密码：admin123）
INSERT INTO `tb_user` (`username`, `email`, `password`, `nickname`, `role`, `status`) VALUES
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '管理员', 'ADMIN', 1);

-- 分配管理员角色
INSERT INTO `tb_user_role` (`user_id`, `role_id`) VALUES (1, 1);

-- 为管理员角色分配所有权限
INSERT INTO `tb_role_permission` (`role_id`, `permission_id`) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (1, 6), (1, 7), (1, 8), (1, 9);

-- 插入默认分类
INSERT INTO `tb_category` (`name`, `description`, `parent_id`, `sort_order`) VALUES
('技术分享', '技术相关的文章', 0, 1),
('生活随笔', '生活感悟和随笔', 0, 2),
('学习笔记', '学习过程中的笔记', 0, 3);

-- 插入默认标签
INSERT INTO `tb_tag` (`tag_name`, `tag_color`, `description`, `sort_order`) VALUES
('Java', '#f56c6c', 'Java技术相关', 1),
('Spring Boot', '#409eff', 'Spring Boot框架', 2),
('前端', '#67c23a', '前端技术', 3),
('数据库', '#e6a23c', '数据库相关', 4),
('随笔', '#909399', '生活随笔', 5);

-- 插入字典类型
INSERT INTO `tb_dict_type` (`dict_type_code`, `dict_type_name`, `description`, `sort_order`, `status`) VALUES
('user_status', '用户状态', '用户账号状态', 1, 1),
('article_status', '文章状态', '文章发布状态', 2, 1),
('comment_status', '评论状态', '评论审核状态', 3, 1),
('device_type', '设备类型', '访问设备类型', 4, 1),
('permission_type', '权限类型', '权限分类类型', 5, 1);

-- 插入字典数据
INSERT INTO `tb_dict_data` (`dict_type_code`, `dict_label`, `dict_value`, `description`, `sort_order`, `status`, `is_default`) VALUES
-- 用户状态
('user_status', '正常', '1', '用户状态正常', 1, 1, 1),
('user_status', '禁用', '0', '用户被禁用', 2, 1, 0),
-- 文章状态
('article_status', '草稿', '0', '文章草稿状态', 1, 1, 0),
('article_status', '已发布', '1', '文章已发布', 2, 1, 1),
-- 评论状态
('comment_status', '待审核', '0', '评论待审核', 1, 1, 0),
('comment_status', '正常', '1', '评论正常显示', 2, 1, 1),
('comment_status', '已删除', '-1', '评论已删除', 3, 1, 0),
-- 设备类型
('device_type', 'PC端', '1', 'PC电脑端', 1, 1, 1),
('device_type', '移动端', '2', '手机移动端', 2, 1, 0),
('device_type', '小程序', '3', '微信小程序', 3, 1, 0),
-- 权限类型
('permission_type', '菜单', '1', '菜单权限', 1, 1, 1),
('permission_type', '按钮', '2', '按钮权限', 2, 1, 0),
('permission_type', '接口', '3', 'API接口权限', 3, 1, 0);

-- 插入系统配置
INSERT INTO `tb_system_config` (`config_key`, `config_value`, `config_name`, `description`, `config_type`, `is_builtin`, `sort_order`) VALUES
-- 博客基本信息
('blog.title', '个人博客系统', '博客标题', '网站标题', 2, 1, 1),
('blog.subtitle', '记录生活，分享技术', '博客副标题', '网站副标题', 2, 1, 2),
('blog.description', '这是一个基于Spring Boot的个人博客系统', '博客描述', '网站描述', 2, 1, 3),
('blog.keywords', '个人博客,技术分享,生活随笔', '博客关键词', '网站关键词', 2, 1, 4),
('blog.author', '博主', '博客作者', '博客作者名称', 2, 1, 5),
('blog.email', '<EMAIL>', '联系邮箱', '博主联系邮箱', 2, 1, 6),
('blog.icp', '', 'ICP备案号', '网站ICP备案号', 2, 1, 7),
-- SEO设置
('seo.title_suffix', ' - 个人博客', 'SEO标题后缀', 'SEO页面标题后缀', 2, 1, 10),
('seo.meta_description', '个人博客系统，记录生活，分享技术', 'SEO描述', 'SEO页面描述', 2, 1, 11),
('seo.meta_keywords', '个人博客,技术分享,生活随笔,Spring Boot', 'SEO关键词', 'SEO页面关键词', 2, 1, 12),
-- 系统设置
('system.comment_audit', '0', '评论审核', '是否开启评论审核：1-开启，0-关闭', 1, 1, 20),
('system.register_enabled', '1', '用户注册', '是否允许用户注册：1-允许，0-禁止', 1, 1, 21),
('system.upload_max_size', '10485760', '上传文件大小限制', '上传文件最大大小（字节）', 1, 1, 22),
('system.page_size', '10', '分页大小', '默认分页大小', 1, 1, 23);

-- ========================================
-- 插入测试数据
-- ========================================

-- 插入测试用户（密码都是：123456）
INSERT INTO `tb_user` (`username`, `email`, `password`, `nickname`, `avatar`, `role`, `status`) VALUES
('zhangsan', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '张三', '/avatars/zhangsan.jpg', 'USER', 1),
('lisi', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '李四', '/avatars/lisi.jpg', 'USER', 1),
('wangwu', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '王五', '/avatars/wangwu.jpg', 'USER', 1),
('zhaoliu', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '赵六', '/avatars/zhaoliu.jpg', 'USER', 1),
('sunqi', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '孙七', '/avatars/sunqi.jpg', 'USER', 1),
('zhouba', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '周八', '/avatars/zhouba.jpg', 'USER', 1),
('wujiu', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '吴九', '/avatars/wujiu.jpg', 'USER', 1),
('zhengshi', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '郑十', '/avatars/zhengshi.jpg', 'USER', 1),
('editor1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '编辑员1', '/avatars/editor1.jpg', 'EDITOR', 1),
('editor2', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '编辑员2', '/avatars/editor2.jpg', 'EDITOR', 1),
('testuser1', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '测试用户1', '/avatars/testuser1.jpg', 'USER', 1),
('testuser2', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '测试用户2', '/avatars/testuser2.jpg', 'USER', 1);

-- 为测试用户分配角色
INSERT INTO `tb_user_role` (`user_id`, `role_id`) VALUES
(2, 3), (3, 3), (4, 3), (5, 3), (6, 3), (7, 3), (8, 3), (9, 3), (12, 3), (13, 3), -- 普通用户
(10, 2), (11, 2); -- 编辑员

-- 插入测试文章
INSERT INTO `tb_article` (`title`, `summary`, `content`, `cover_image`, `category_id`, `author_id`, `view_count`, `like_count`, `comment_count`, `is_published`, `is_top`) VALUES
('Spring Boot入门指南', 'Spring Boot是一个基于Spring框架的快速开发框架，本文将介绍如何快速上手Spring Boot开发。',
'<h2>什么是Spring Boot</h2><p>Spring Boot是由Pivotal团队提供的全新框架，其设计目的是用来简化新Spring应用的初始搭建以及开发过程。</p><h2>主要特性</h2><ul><li>创建独立的Spring应用程序</li><li>嵌入的Tomcat，无需部署WAR文件</li><li>简化Maven配置</li><li>自动配置Spring</li></ul><h2>快速开始</h2><p>使用Spring Initializr可以快速创建一个Spring Boot项目...</p>',
'/images/spring-boot-cover.jpg', 1, 1, 156, 23, 8, 1, 1),

('Vue 3 Composition API详解', 'Vue 3引入了Composition API，这是一个全新的API设计，让我们能够更好地组织和复用代码逻辑。',
'<h2>Composition API简介</h2><p>Composition API是Vue 3中最重要的新特性之一，它提供了一种更灵活的方式来组织组件逻辑。</p><h2>基本用法</h2><pre><code>import { ref, reactive } from "vue";\n\nexport default {\n  setup() {\n    const count = ref(0);\n    const state = reactive({ name: "Vue 3" });\n    \n    return { count, state };\n  }\n}</code></pre><h2>优势</h2><ul><li>更好的TypeScript支持</li><li>更灵活的逻辑复用</li><li>更好的代码组织</li></ul>',
'/images/vue3-cover.jpg', 1, 10, 89, 15, 5, 1, 0),

('MySQL性能优化实战', '本文将从索引优化、查询优化、配置优化等多个角度来讲解MySQL性能优化的实战技巧。',
'<h2>索引优化</h2><p>索引是提高MySQL查询性能的最重要手段之一。</p><h3>索引类型</h3><ul><li>主键索引</li><li>唯一索引</li><li>普通索引</li><li>复合索引</li></ul><h2>查询优化</h2><p>编写高效的SQL语句是性能优化的关键...</p><h2>配置优化</h2><p>合理的MySQL配置可以显著提升数据库性能...</p>',
'/images/mysql-cover.jpg', 1, 11, 234, 31, 12, 1, 0),

('生活中的小确幸', '记录生活中那些微小但美好的瞬间，感受平凡日子里的温暖与感动。',
'<p>今天早上起床，阳光正好透过窗帘洒在床上，那一刻觉得特别温暖。</p><p>路过楼下的咖啡店，老板娘还记得我喜欢喝拿铁不加糖，这种被记住的感觉真好。</p><p>晚上和朋友视频聊天，虽然相隔千里，但友情依然如初。</p><p>这些看似平凡的小事，却构成了生活中最珍贵的回忆...</p>',
'/images/life-cover.jpg', 2, 2, 67, 8, 3, 1, 0),

('Docker容器化部署实践', '本文介绍如何使用Docker进行应用容器化部署，包括Dockerfile编写、镜像构建、容器运行等内容。',
'<h2>Docker简介</h2><p>Docker是一个开源的容器化平台，可以让开发者打包应用以及依赖包到一个轻量级、可移植的容器中。</p><h2>Dockerfile编写</h2><pre><code>FROM openjdk:11-jre-slim\nCOPY target/app.jar app.jar\nEXPOSE 8080\nENTRYPOINT ["java", "-jar", "/app.jar"]</code></pre><h2>镜像构建</h2><p>使用docker build命令构建镜像...</p>',
'/images/docker-cover.jpg', 1, 10, 145, 19, 7, 1, 0),

('读书笔记：《代码整洁之道》', '《代码整洁之道》是每个程序员都应该读的经典书籍，本文记录了我的读书心得和实践体会。',
'<h2>什么是整洁代码</h2><p>整洁代码是能够被人轻松阅读和理解的代码。它不仅仅是能够运行的代码，更是优雅、简洁、易于维护的代码。</p><h2>命名的艺术</h2><p>好的命名能够让代码自文档化...</p><h2>函数设计原则</h2><ul><li>函数应该短小</li><li>只做一件事</li><li>使用描述性的名称</li></ul>',
'/images/clean-code-cover.jpg', 3, 3, 78, 12, 4, 1, 0),

('Redis缓存设计模式', '介绍Redis在实际项目中的应用模式，包括缓存穿透、缓存雪崩、缓存击穿等问题的解决方案。',
'<h2>Redis基础</h2><p>Redis是一个开源的内存数据结构存储系统，可以用作数据库、缓存和消息代理。</p><h2>常见问题及解决方案</h2><h3>缓存穿透</h3><p>查询一个不存在的数据，由于缓存是不命中时被动写的，并且出于容错考虑，如果从存储层查不到数据则不写入缓存...</p>',
'/images/redis-cover.jpg', 1, 11, 198, 25, 9, 1, 0),

('周末的慢时光', '忙碌的工作日结束了，周末是属于自己的时间，让我们一起享受这份难得的慢时光。',
'<p>周六的早晨，没有闹钟的催促，自然醒来的感觉真好。</p><p>泡一壶好茶，翻开一本喜欢的书，时间仿佛都慢了下来。</p><p>下午去公园走走，看看绿树成荫，听听鸟儿歌唱，心情格外舒畅。</p><p>晚上在家做一顿丰盛的晚餐，享受烹饪的乐趣...</p>',
'/images/weekend-cover.jpg', 2, 4, 45, 6, 2, 1, 0),

('JavaScript异步编程进阶', '深入理解JavaScript异步编程，从回调函数到Promise，再到async/await的演进历程。',
'<h2>异步编程的重要性</h2><p>JavaScript是单线程语言，异步编程是处理耗时操作的重要手段。</p><h2>回调函数</h2><p>最早的异步解决方案，但容易产生回调地狱...</p><h2>Promise</h2><p>ES6引入的Promise解决了回调地狱问题...</p><h2>async/await</h2><p>ES2017引入的语法糖，让异步代码看起来像同步代码...</p>',
'/images/js-async-cover.jpg', 1, 2, 167, 21, 6, 1, 0),

('学习笔记：设计模式', '设计模式是软件开发中的重要概念，本文总结了常用的设计模式及其应用场景。',
'<h2>什么是设计模式</h2><p>设计模式是在软件设计中常见问题的典型解决方案。</p><h2>创建型模式</h2><ul><li>单例模式</li><li>工厂模式</li><li>建造者模式</li></ul><h2>结构型模式</h2><ul><li>适配器模式</li><li>装饰器模式</li><li>代理模式</li></ul><h2>行为型模式</h2><ul><li>观察者模式</li><li>策略模式</li><li>命令模式</li></ul>',
'/images/design-pattern-cover.jpg', 3, 5, 123, 16, 8, 1, 0),

('微服务架构实践', '介绍微服务架构的设计理念、技术选型和实施过程中的经验总结。',
'<h2>微服务架构概述</h2><p>微服务架构是一种将单一应用程序开发为一组小型服务的方法。</p><h2>技术选型</h2><ul><li>Spring Cloud</li><li>Docker</li><li>Kubernetes</li><li>API Gateway</li></ul><h2>实施挑战</h2><p>服务拆分、数据一致性、分布式事务等问题需要仔细考虑...</p>',
'/images/microservice-cover.jpg', 1, 1, 289, 35, 15, 1, 0),

('旅行日记：江南水乡', '记录一次江南水乡的旅行经历，感受古镇的宁静与美好。',
'<p>这次江南之行，让我深深感受到了水乡古镇的独特魅力。</p><p>乌镇的小桥流水，西塘的烟雨朦胧，每一处风景都如诗如画。</p><p>漫步在青石板路上，听着脚步声在巷子里回响，仿佛穿越到了古代。</p><p>品尝当地的特色小吃，感受浓浓的人情味...</p>',
'/images/jiangnan-cover.jpg', 2, 6, 92, 11, 5, 1, 0);

-- 插入文章标签关联数据
INSERT INTO `tb_article_tag` (`article_id`, `tag_id`) VALUES
-- Spring Boot入门指南
(1, 2), (1, 1),
-- Vue 3 Composition API详解
(2, 3),
-- MySQL性能优化实战
(3, 4), (3, 1),
-- 生活中的小确幸
(4, 5),
-- Docker容器化部署实践
(5, 1), (5, 2),
-- 读书笔记：《代码整洁之道》
(6, 1),
-- Redis缓存设计模式
(7, 4), (7, 1),
-- 周末的慢时光
(8, 5),
-- JavaScript异步编程进阶
(9, 3),
-- 学习笔记：设计模式
(10, 1),
-- 微服务架构实践
(11, 1), (11, 2),
-- 旅行日记：江南水乡
(12, 5);

-- 插入评论数据（演示新的两层评论结构）
INSERT INTO `tb_comment` (`article_id`, `user_id`, `parent_id`, `reply_to_id`, `content`, `ip_address`, `status`) VALUES
-- 文章1的评论（演示：a用户评论 -> b用户回复a -> c用户回复b）
(1, 2, 0, 0, '这篇文章写得很好，对新手很友好！', '*************', 1),
(1, 3, 1, 2, '同意，我也是通过这篇文章入门的', '*************', 1),
(1, 4, 1, 3, '是的，Spring Boot确实简化了很多配置', '*************', 1),
(1, 5, 1, 4, '感谢分享，学到了很多', '*************', 1),

-- 文章2的评论（演示：a用户评论 -> b用户回复a -> c用户回复b -> d用户回复c）
(2, 2, 0, 0, 'Vue 3的Composition API确实很强大', '*************', 1),
(2, 3, 2, 2, 'TypeScript支持确实好了很多', '*************', 1),
(2, 4, 2, 3, '对于大型项目来说，逻辑复用更方便了', '*************', 1),
(2, 5, 2, 4, '代码示例很实用，谢谢分享', '*************', 1),

-- 文章3的评论（演示：a用户评论 -> b用户回复a -> c用户回复a）
(3, 2, 0, 0, 'MySQL性能优化部分讲得很透彻', '*************', 1),
(3, 3, 3, 2, '实战经验很宝贵，学到了不少', '*************', 1),
(3, 4, 3, 2, '是的，特别是复合索引的使用技巧', '*************', 1),



-- 插入友情链接数据
INSERT INTO `tb_link` (`name`, `url`, `description`, `logo`, `status`, `sort_order`) VALUES
('Vue.js官网', 'https://vuejs.org/', 'Vue.js官方网站', '/logos/vue-logo.png', 1, 1),
('Spring Boot官网', 'https://spring.io/projects/spring-boot', 'Spring Boot官方网站', '/logos/spring-logo.png', 1, 2),
('GitHub', 'https://github.com/', '全球最大的代码托管平台', '/logos/github-logo.png', 1, 3),
('Stack Overflow', 'https://stackoverflow.com/', '程序员问答社区', '/logos/stackoverflow-logo.png', 1, 4),
('MDN Web Docs', 'https://developer.mozilla.org/', 'Web开发者文档', '/logos/mdn-logo.png', 1, 5),
('阮一峰的网络日志', 'https://www.ruanyifeng.com/blog/', '知名技术博客', '/logos/ruanyifeng-logo.png', 1, 6),
('掘金', 'https://juejin.cn/', '技术社区', '/logos/juejin-logo.png', 1, 7),
('博客园', 'https://www.cnblogs.com/', '开发者社区', '/logos/cnblogs-logo.png', 1, 8),
('CSDN', 'https://www.csdn.net/', 'IT技术社区', '/logos/csdn-logo.png', 1, 9),
('开源中国', 'https://www.oschina.net/', '开源技术社区', '/logos/oschina-logo.png', 1, 10),
('Redis官网', 'https://redis.io/', 'Redis官方网站', '/logos/redis-logo.png', 1, 11),
('MySQL官网', 'https://www.mysql.com/', 'MySQL官方网站', '/logos/mysql-logo.png', 1, 12);

-- 插入访问日志数据（最近30天的模拟数据）
INSERT INTO `tb_visit_log` (`ip_address`, `user_agent`, `request_url`, `referer`, `visit_time`) VALUES
('*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '/', '', DATE_SUB(NOW(), INTERVAL 1 DAY)),
('*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '/article/1', '/', DATE_SUB(NOW(), INTERVAL 1 DAY)),
('*************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', '/category/1', '/', DATE_SUB(NOW(), INTERVAL 1 DAY)),
('*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)', '/', '', DATE_SUB(NOW(), INTERVAL 2 DAY)),
('*************', 'Mozilla/5.0 (Android 11; Mobile; rv:68.0) Gecko/68.0', '/article/2', '/', DATE_SUB(NOW(), INTERVAL 2 DAY)),
('*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '/search?keyword=spring', '/category/1', DATE_SUB(NOW(), INTERVAL 2 DAY)),
('*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '/article/3', '/', DATE_SUB(NOW(), INTERVAL 3 DAY)),
('*************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', '/about', '/', DATE_SUB(NOW(), INTERVAL 3 DAY)),
('*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '/', '', DATE_SUB(NOW(), INTERVAL 3 DAY)),
('*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)', '/article/4', '/', DATE_SUB(NOW(), INTERVAL 4 DAY)),
('*************', 'Mozilla/5.0 (Android 11; Mobile; rv:68.0) Gecko/68.0', '/category/2', '/', DATE_SUB(NOW(), INTERVAL 4 DAY)),
('*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '/article/5', '/search?keyword=docker', DATE_SUB(NOW(), INTERVAL 4 DAY)),
('*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '/', '', DATE_SUB(NOW(), INTERVAL 5 DAY)),
('*************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', '/article/6', '/', DATE_SUB(NOW(), INTERVAL 5 DAY)),
('*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)', '/category/3', '/', DATE_SUB(NOW(), INTERVAL 5 DAY)),
('*************', 'Mozilla/5.0 (Android 11; Mobile; rv:68.0) Gecko/68.0', '/article/7', '/', DATE_SUB(NOW(), INTERVAL 6 DAY)),
('*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '/', '', DATE_SUB(NOW(), INTERVAL 6 DAY)),
('*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '/article/8', '/', DATE_SUB(NOW(), INTERVAL 6 DAY)),
('*************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', '/search?keyword=javascript', '/', DATE_SUB(NOW(), INTERVAL 7 DAY)),
('*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)', '/article/9', '/', DATE_SUB(NOW(), INTERVAL 7 DAY)),
('*************', 'Mozilla/5.0 (Android 11; Mobile; rv:68.0) Gecko/68.0', '/', '', DATE_SUB(NOW(), INTERVAL 7 DAY));

-- 插入文件上传记录数据
INSERT INTO `tb_file` (`original_name`, `file_name`, `file_path`, `file_size`, `file_type`, `upload_user_id`) VALUES
('spring-boot-cover.jpg', 'spring-boot-cover-20250730001.jpg', '/uploads/images/2025/07/30/spring-boot-cover-20250730001.jpg', 245760, 'image/jpeg', 1),
('vue3-cover.jpg', 'vue3-cover-20250730002.jpg', '/uploads/images/2025/07/30/vue3-cover-20250730002.jpg', 189432, 'image/jpeg', 10),
('mysql-cover.jpg', 'mysql-cover-20250730003.jpg', '/uploads/images/2025/07/30/mysql-cover-20250730003.jpg', 298765, 'image/jpeg', 11),
('life-cover.jpg', 'life-cover-20250730004.jpg', '/uploads/images/2025/07/30/life-cover-20250730004.jpg', 156789, 'image/jpeg', 2),
('docker-cover.jpg', 'docker-cover-20250730005.jpg', '/uploads/images/2025/07/30/docker-cover-20250730005.jpg', 234567, 'image/jpeg', 10),
('clean-code-cover.jpg', 'clean-code-cover-20250730006.jpg', '/uploads/images/2025/07/30/clean-code-cover-20250730006.jpg', 198765, 'image/jpeg', 3),
('redis-cover.jpg', 'redis-cover-20250730007.jpg', '/uploads/images/2025/07/30/redis-cover-20250730007.jpg', 267890, 'image/jpeg', 11),
('weekend-cover.jpg', 'weekend-cover-20250730008.jpg', '/uploads/images/2025/07/30/weekend-cover-20250730008.jpg', 145678, 'image/jpeg', 4),
('js-async-cover.jpg', 'js-async-cover-20250730009.jpg', '/uploads/images/2025/07/30/js-async-cover-20250730009.jpg', 223456, 'image/jpeg', 2),
('design-pattern-cover.jpg', 'design-pattern-cover-20250730010.jpg', '/uploads/images/2025/07/30/design-pattern-cover-20250730010.jpg', 187654, 'image/jpeg', 5),
('microservice-cover.jpg', 'microservice-cover-20250730011.jpg', '/uploads/images/2025/07/30/microservice-cover-20250730011.jpg', 312456, 'image/jpeg', 1),
('jiangnan-cover.jpg', 'jiangnan-cover-20250730012.jpg', '/uploads/images/2025/07/30/jiangnan-cover-20250730012.jpg', 278901, 'image/jpeg', 6),
('avatar-admin.jpg', 'avatar-admin-20250730013.jpg', '/uploads/avatars/2025/07/30/avatar-admin-20250730013.jpg', 98765, 'image/jpeg', 1),
('avatar-zhangsan.jpg', 'avatar-zhangsan-20250730014.jpg', '/uploads/avatars/2025/07/30/avatar-zhangsan-20250730014.jpg', 87654, 'image/jpeg', 2),
('avatar-lisi.jpg', 'avatar-lisi-20250730015.jpg', '/uploads/avatars/2025/07/30/avatar-lisi-20250730015.jpg', 76543, 'image/jpeg', 3),
('document.pdf', 'document-20250730016.pdf', '/uploads/documents/2025/07/30/document-20250730016.pdf', 1234567, 'application/pdf', 1),
('code-example.zip', 'code-example-20250730017.zip', '/uploads/files/2025/07/30/code-example-20250730017.zip', 2345678, 'application/zip', 10),
('presentation.pptx', 'presentation-20250730018.pptx', '/uploads/documents/2025/07/30/presentation-20250730018.pptx', 3456789, 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 11);

-- 关于页面信息表
CREATE TABLE `tb_about_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `title` varchar(200) NOT NULL COMMENT '标题',
  `description` json COMMENT '个人描述（JSON数组格式）',
  `contact_email` varchar(100) COMMENT '联系邮箱',
  `contact_github` varchar(100) COMMENT 'GitHub链接',
  `contact_location` varchar(100) COMMENT '位置信息',
  `social_links` json COMMENT '社交链接（JSON数组格式）',
  `skills_frontend` json COMMENT '前端技能（JSON数组格式）',
  `skills_backend` json COMMENT '后端技能（JSON数组格式）',
  `skills_database` json COMMENT '数据库技能（JSON数组格式）',
  `skills_tools` json COMMENT '工具技能（JSON数组格式）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_about_info_user` FOREIGN KEY (`user_id`) REFERENCES `tb_user` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='关于页面信息表';

-- 插入默认的关于页面信息（为管理员用户）
INSERT INTO `tb_about_info` (
  `user_id`,
  `title`, 
  `description`, 
  `contact_email`, 
  `contact_github`, 
  `contact_location`,
  `social_links`,
  `skills_frontend`,
  `skills_backend`,
  `skills_database`,
  `skills_tools`
) VALUES (
  1,
  '全栈开发工程师 | 技术博主 | 开源爱好者',
  JSON_ARRAY(
    '你好！我是一名热爱技术的全栈开发工程师，专注于Web开发和技术分享。',
    '拥有多年的开发经验，熟悉前端和后端技术栈。',
    '我喜欢学习新技术，探索最佳实践，并通过博客分享我的经验和见解。',
    '希望能够帮助更多的开发者成长，也期待与大家交流学习。'
  ),
  '<EMAIL>',
  'github.com/admin',
  '中国，北京',
  JSON_ARRAY(
    JSON_OBJECT('name', 'GitHub', 'icon', 'Link', 'url', 'https://github.com/admin'),
    JSON_OBJECT('name', 'Email', 'icon', 'Message', 'url', 'mailto:<EMAIL>'),
    JSON_OBJECT('name', '微博', 'icon', 'Share', 'url', 'https://weibo.com/admin')
  ),
  JSON_ARRAY('Vue.js', 'React', 'JavaScript', 'TypeScript', 'HTML5', 'CSS3', 'Sass', 'Element Plus'),
  JSON_ARRAY('Java', 'Spring Boot', 'Node.js', 'Express', 'Python', 'Django', 'RESTful API'),
  JSON_ARRAY('MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'Elasticsearch'),
  JSON_ARRAY('Git', 'Docker', 'Linux', 'Nginx', 'Webpack', 'Vite', 'Jenkins')
);

-- 数据插入完成提示
SELECT '数据库初始化完成！' AS message;
SELECT CONCAT('共插入用户: ', COUNT(*), ' 条') AS user_count FROM tb_user;
SELECT CONCAT('共插入文章: ', COUNT(*), ' 条') AS article_count FROM tb_article;
SELECT CONCAT('共插入评论: ', COUNT(*), ' 条') AS comment_count FROM tb_comment;
SELECT CONCAT('共插入友情链接: ', COUNT(*), ' 条') AS link_count FROM tb_link;
SELECT CONCAT('共插入访问日志: ', COUNT(*), ' 条') AS visit_log_count FROM tb_visit_log;
SELECT CONCAT('共插入文件记录: ', COUNT(*), ' 条') AS file_count FROM tb_file;
