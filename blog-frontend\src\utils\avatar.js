/**
 * 头像URL处理工具函数
 */

/**
 * 获取头像完整URL
 * @param {string} avatar - 头像路径
 * @returns {string} 完整的头像URL
 */
export const getAvatarUrl = (avatar) => {
  if (!avatar) {
    return '/default-avatar.png'
  }
  
  // 如果已经是完整URL，直接返回
  if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
    return avatar
  }
  
  // 如果是API访问路径，直接返回
  if (avatar.startsWith('/api/files/access')) {
    return `${window.location.origin}${avatar}`
  }
  
  // 如果是相对路径，添加基础URL
  if (avatar.startsWith('/')) {
    return `${window.location.origin}${avatar}`
  }
  
  // 默认头像
  return '/default-avatar.png'
}

/**
 * 获取默认头像URL
 * @returns {string} 默认头像URL
 */
export const getDefaultAvatarUrl = () => {
  return '/default-avatar.png'
}

/**
 * 验证头像URL是否有效
 * @param {string} avatar - 头像路径
 * @returns {boolean} 是否有效
 */
export const isValidAvatarUrl = (avatar) => {
  if (!avatar) return false
  
  // 检查是否是有效的URL格式
  try {
    const url = new URL(avatar.startsWith('/') ? `${window.location.origin}${avatar}` : avatar)
    return url.protocol === 'http:' || url.protocol === 'https:'
  } catch {
    return false
  }
} 